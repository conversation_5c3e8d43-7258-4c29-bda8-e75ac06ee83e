// ==UserScript==
// @name         Aria Downloader 媒体捕获工具 (终极重构版)
// @namespace    aria-downloader.io
// @version      5.0.0
// @description  基于Linus哲学的终极重构版：简洁、高效、可靠的媒体流捕获工具，专注核心功能，追求完美执行
// <AUTHOR> Project (Ultimate Refactor Edition)
// @match        *://*.youtube.com/*
// @match        *://*.youtube-nocookie.com/*
// @match        *://*.bilibili.com/*
// @match        *://*.vimeo.com/*
// @match        *://*.netflix.com/*
// @match        *://*.twitch.tv/*
// @match        *://*.hulu.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_deleteValue
// @grant        GM_listValues
// @run-at       document-start
// ==/UserScript==

console.log('[Aria Bootstrapper] Ultimate Refactor v5.0.0 - <PERSON><PERSON><PERSON> injected successfully');

/**
 * ===== Aria Downloader v5.0.0 终极重构版使用指南 =====
 *
 * 🎯 设计哲学 (Based on Linus Torvalds Philosophy)
 * "Talk is cheap. Show me the code." - 专注实现，拒绝花哨
 * "Good taste" in software design - 追求简洁优雅的架构
 * "Do one thing and do it well" - 专注媒体流捕获这一核心功能
 *
 * 📋 核心特性 (Core Features)
 * 1. 🔥 MediaSource API 劫持 - 无侵入式捕获正在播放的媒体流
 * 2. 🚀 智能自适应变速 - 根据缓冲状态动态调整播放速度
 * 3. 🎯 模块化站点提取器 - 支持YouTube、Bilibili等主流平台
 * 4. 💾 IndexedDB持久化存储 - 支持大文件捕获和断点续传
 * 5. 🛡️ Shadow DOM隔离UI - 完全不干扰页面原有样式
 * 6. 🔧 自愈数据库机制 - 自动处理版本冲突和数据损坏
 *
 * 🚀 快速开始
 * 1. 访问支持的视频网站并播放视频
 * 2. 脚本自动开始捕获媒体流
 * 3. 点击右下角浮动图标打开控制面板
 * 4. 捕获完成后点击下载按钮
 * 5. 使用FFmpeg合并音视频: ffmpeg -i video.webm -i audio.webm -c copy output.mp4
 *
 * 💡 Linus式设计原则
 * - "Perfection is achieved not when there is nothing more to add, but when there is nothing left to take away"
 * - 每一行代码都有其存在的理由
 * - 优先考虑可靠性和性能，而非功能数量
 * - 代码应该是自解释的，注释用于解释"为什么"而非"是什么"
 */

// 用户配置 - 基于400.js的成熟配置，经过实战验证
const ARIA_CONFIG = {
    // 智能变速参数
    SPEED_HIGH_WATERMARK: 45,      // 高水位线：缓冲超过45秒时加速
    SPEED_LOW_WATERMARK: 15,       // 低水位线：缓冲低于15秒时恢复正常速度
    SPEED_SMOOTHING_FACTOR: 0.1,   // 速度平滑因子：避免速度突变
    DEFAULT_MAX_SPEED: 8.0,        // 默认最大播放速度

    // 系统参数
    HEARTBEAT_INTERVAL: 5000,      // 心跳间隔
    HEARTBEAT_TIMEOUT: 60000,      // 心跳超时
    STALL_CHECK_INTERVAL: 2000,    // 停滞检测间隔
    STALL_THRESHOLD: 5,            // 停滞阈值

    // 存储参数
    MAX_CAPTURE_SIZE_MB: 2048,     // 最大捕获大小2GB
    STORAGE_WARNING_THRESHOLD: 0.85,  // 存储警告阈值85%
    STORAGE_CRITICAL_THRESHOLD: 0.90, // 存储临界阈值90%
    AUTO_CLEANUP_ENABLED: true,    // 启用自动清理

    // 性能参数
    CHUNK_BATCH_SIZE: 50,          // 批处理大小
    RETRY_ATTEMPTS: 3,             // 重试次数
    RETRY_DELAY: 1000,             // 重试延迟
    CLEANUP_INTERVAL: 600000,      // 清理间隔10分钟
    SESSION_EXPIRE_TIME: 1800000,  // 会话过期时间30分钟

    // UI参数
    BLOB_URL_REVOKE_DELAY: 15000,  // Blob URL释放延迟
    VIDEO_ELEMENT_TIMEOUT: 20000,  // 视频元素检测超时
    ACTIVATION_DELAY: 750          // 激活延迟
};

(function() {
    'use strict';

    // iframe检测 - 确保只在顶层页面执行
    if (window.self !== window.top) {
        console.log('[Aria] Script blocked in iframe');
        return;
    }

    const AriaDownloader = (() => {
        // 日志管理器 - 继承400.js的成熟实现
        class Logger {
            constructor() {
                this.level = 'info';
            }

            setLevel(level) {
                this.level = level;
                this.info(`Log level set to: ${level}`);
            }

            log(level, ...args) {
                const levels = { none: 0, error: 1, info: 2, debug: 3 };
                if (levels[level] <= levels[this.level]) {
                    console.log(`[Aria ${level.toUpperCase()}]`, ...args);
                }
            }

            error(...args) { this.log('error', ...args); }
            info(...args) { this.log('info', ...args); }
            debug(...args) { this.log('debug', ...args); }
        }

        // 心跳管理器 - 无状态版本，专注可靠性
        class HeartbeatManager {
            constructor() {
                this.heartbeatKey = 'aria_active_sessions_v5';
            }

            updateSession(sessionId, sessionData) {
                try {
                    const heartbeats = this.getAllStoredSessions();
                    heartbeats[sessionId] = {
                        timestamp: Date.now(),
                        url: window.location.href,
                        ...sessionData
                    };
                    localStorage.setItem(this.heartbeatKey, JSON.stringify(heartbeats));
                } catch (e) {
                    AriaController.logger.error('Failed to update heartbeat:', e);
                    if (e.name === 'QuotaExceededError') {
                        AriaController.getInstance().requestCleanup();
                    }
                }
            }

            removeSession(sessionId) {
                try {
                    const heartbeats = this.getAllStoredSessions();
                    delete heartbeats[sessionId];
                    localStorage.setItem(this.heartbeatKey, JSON.stringify(heartbeats));
                } catch (e) {
                    AriaController.logger.error('Failed to remove heartbeat:', e);
                }
            }

            getActiveSessions() {
                const now = Date.now();
                const allSessions = this.getAllStoredSessions();
                const active = {};

                for (const [id, data] of Object.entries(allSessions)) {
                    if (now - data.timestamp < ARIA_CONFIG.HEARTBEAT_TIMEOUT) {
                        active[id] = data;
                    }
                }

                return active;
            }

            getAllStoredSessions() {
                try {
                    return JSON.parse(localStorage.getItem(this.heartbeatKey) || '{}');
                } catch (e) {
                    AriaController.logger.error('Failed to parse heartbeats:', e);
                    return {};
                }
            }

            cleanupExpiredSessions() {
                const now = Date.now();
                const allSessions = this.getAllStoredSessions();
                const cleaned = {};
                let hasChanges = false;

                for (const [id, data] of Object.entries(allSessions)) {
                    if (now - data.timestamp < ARIA_CONFIG.HEARTBEAT_TIMEOUT * 2) {
                        cleaned[id] = data;
                    } else {
                        hasChanges = true;
                    }
                }

                if (hasChanges) {
                    try {
                        localStorage.setItem(this.heartbeatKey, JSON.stringify(cleaned));
                    } catch (e) {
                        AriaController.logger.error('Failed to cleanup heartbeats:', e);
                    }
                }

                return hasChanges;
            }
        }

        // 数据库管理器 - 继承400.js的自愈机制
        class DBManager {
            constructor() {
                this.dbName = 'AriaDownloaderDB_v5';
                this.version = 1;
                this.db = null;
            }

            async init() {
                try {
                    await this.openDatabase();
                } catch (error) {
                    if (error.name === 'VersionError' || error.name === 'InvalidStateError') {
                        AriaController.logger.error('DB error detected. Attempting recovery...');
                        if (this.db) this.db.close();
                        await this.destroyDatabase();
                        AriaController.logger.info('Corrupted DB deleted. Retrying initialization.');
                        await this.openDatabase();
                    } else {
                        throw error;
                    }
                }
            }

            async destroyDatabase() {
                return new Promise((resolve, reject) => {
                    const req = indexedDB.deleteDatabase(this.dbName);
                    req.onsuccess = resolve;
                    req.onerror = reject;
                    req.onblocked = () => {
                        AriaController.logger.warn('DB deletion blocked, waiting...');
                        setTimeout(resolve, 1000);
                    };
                });
            }

            async openDatabase() {
                return new Promise((resolve, reject) => {
                    const request = indexedDB.open(this.dbName, this.version);

                    request.onerror = (e) => {
                        AriaController.logger.error('Failed to open database:', e.target.error);
                        reject(e.target.error);
                    };

                    request.onsuccess = (e) => {
                        this.db = e.target.result;
                        this.db.onerror = (event) => {
                            AriaController.logger.error('Database error:', event.target.error);
                        };
                        resolve();
                    };

                    request.onupgradeneeded = (e) => {
                        const db = e.target.result;
                        if (db.objectStoreNames.contains('chunks')) {
                            db.deleteObjectStore('chunks');
                        }
                        const store = db.createObjectStore('chunks', { keyPath: 'id', autoIncrement: true });
                        store.createIndex('sessionId', 'sessionId', { unique: false });
                        store.createIndex('type', 'type', { unique: false });
                        store.createIndex('timestamp', 'timestamp', { unique: false });
                    };
                });
            }

            async saveChunk(sessionId, type, data) {
                if (!this.db) throw new Error('Database not initialized');

                const transaction = this.db.transaction(['chunks'], 'readwrite');
                return new Promise((resolve, reject) => {
                    const request = transaction.objectStore('chunks').add({
                        sessionId,
                        type,
                        data,
                        timestamp: Date.now()
                    });

                    request.onsuccess = resolve;
                    request.onerror = (e) => {
                        if (e.target.error.name === 'QuotaExceededError') {
                            AriaController.logger.error('Storage quota exceeded');
                            reject(new Error('QUOTA_EXCEEDED'));
                        } else {
                            reject(e.target.error);
                        }
                    };
                });
            }

            async getChunks(sessionId, type) {
                if (!this.db) return [];

                const transaction = this.db.transaction(['chunks'], 'readonly');
                const index = transaction.objectStore('chunks').index('sessionId');

                return new Promise((resolve, reject) => {
                    const request = index.getAll(sessionId);
                    request.onsuccess = () => {
                        const chunks = request.result;
                        if (type) {
                            resolve(chunks.filter(c => c.type === type).sort((a, b) => a.id - b.id));
                        } else {
                            resolve(chunks.sort((a, b) => a.id - b.id));
                        }
                    };
                    request.onerror = (e) => reject(e.target.error);
                });
            }

            async deleteSession(sessionId) {
                if (!this.db) return;

                const transaction = this.db.transaction(['chunks'], 'readwrite');
                const store = transaction.objectStore('chunks');
                const index = store.index('sessionId');

                return new Promise((resolve, reject) => {
                    const request = index.openCursor(IDBKeyRange.only(sessionId));
                    request.onsuccess = (e) => {
                        const cursor = e.target.result;
                        if (cursor) {
                            cursor.delete();
                            cursor.continue();
                        } else {
                            resolve();
                        }
                    };
                    request.onerror = reject;
                });
            }

            async getAllSessionIds() {
                if (!this.db) return [];

                const transaction = this.db.transaction(['chunks'], 'readonly');
                const store = transaction.objectStore('chunks');
                const index = store.index('sessionId');

                return new Promise((resolve, reject) => {
                    const request = index.getAllKeys();
                    request.onsuccess = () => {
                        const uniqueIds = [...new Set(request.result)];
                        resolve(uniqueIds);
                    };
                    request.onerror = (e) => reject(e.target.error);
                });
            }

            async checkStorageUsage() {
                if ('storage' in navigator && 'estimate' in navigator.storage) {
                    try {
                        const estimate = await navigator.storage.estimate();
                        const usagePercent = (estimate.usage / estimate.quota) * 100;
                        return { usage: estimate.usage, quota: estimate.quota, percent: usagePercent };
                    } catch (e) {
                        AriaController.logger.error('Failed to check storage usage:', e);
                    }
                }
                return null;
            }
        }

        // 站点提取器架构 - 基于400.js的成熟模块化设计
        const SiteExtractors = {
            // YouTube提取器
            YouTube: {
                name: 'YouTubeExtractor',

                match: (url) => {
                    const hostname = new URL(url).hostname.toLowerCase();
                    return hostname.includes('youtube.com') || hostname.includes('youtu.be');
                },

                extractVideoId: (url) => {
                    try {
                        let match = url.match(/[?&]v=([^&]+)/);
                        if (match) return match[1];

                        match = url.match(/youtu\.be\/([^?]+)/);
                        if (match) return match[1];

                        match = url.match(/\/embed\/([^?]+)/);
                        if (match) return match[1];

                        return null;
                    } catch (e) {
                        AriaController.logger.error('YouTube ID extraction error:', e);
                        return null;
                    }
                },

                extractMetadata: (doc) => {
                    try {
                        const title = doc.querySelector('meta[property="og:title"]')?.content ||
                                     doc.title.replace(' - YouTube', '').trim() ||
                                     'YouTube Video';

                        const uploader = doc.querySelector('meta[name="author"]')?.content ||
                                        doc.querySelector('#owner-name a, #channel-name a')?.textContent?.trim() ||
                                        'Unknown Channel';

                        const thumbnailUrl = doc.querySelector('meta[property="og:image"]')?.content ||
                                           doc.querySelector('link[rel="image_src"]')?.href;

                        return {
                            title: title.substring(0, 100),
                            uploader: uploader.substring(0, 50),
                            thumbnailUrl: thumbnailUrl,
                            resolution: { width: 0, height: 0 }
                        };
                    } catch (e) {
                        AriaController.logger.error('YouTube metadata extraction error:', e);
                        return this.getDefaultMetadata();
                    }
                },

                getSafeFilename: (metadata) => {
                    const title = metadata.title || 'YouTube Video';
                    const uploader = metadata.uploader || 'Unknown Channel';
                    const safeTitle = title.replace(/[\\/:*?"<>|]/g, '-').substring(0, 60);
                    const safeUploader = uploader.replace(/[\\/:*?"<>|]/g, '-').substring(0, 30);
                    return `[${safeUploader}] ${safeTitle}`;
                },

                getDefaultMetadata: () => ({
                    title: 'YouTube Video',
                    uploader: 'Unknown Channel',
                    thumbnailUrl: null,
                    resolution: { width: 0, height: 0 }
                })
            },

            // Bilibili提取器
            Bilibili: {
                name: 'BilibiliExtractor',

                match: (url) => {
                    const hostname = new URL(url).hostname.toLowerCase();
                    return hostname.includes('bilibili.com');
                },

                extractVideoId: (url) => {
                    try {
                        let match = url.match(/\/video\/(BV[^/?]+)/);
                        if (match) return match[1];

                        match = url.match(/\/video\/(av\d+)/);
                        if (match) return match[1];

                        return null;
                    } catch (e) {
                        AriaController.logger.error('Bilibili ID extraction error:', e);
                        return null;
                    }
                },

                extractMetadata: (doc) => {
                    try {
                        const title = doc.querySelector('meta[property="og:title"]')?.content ||
                                     doc.querySelector('meta[name="title"]')?.content ||
                                     doc.title.replace('_哔哩哔哩_bilibili', '').trim() ||
                                     'Bilibili Video';

                        const uploader = doc.querySelector('meta[name="author"]')?.content ||
                                        doc.querySelector('.username, .up-name')?.textContent?.trim() ||
                                        'Unknown UP主';

                        const thumbnailUrl = doc.querySelector('meta[property="og:image"]')?.content ||
                                           doc.querySelector('.video-pic img')?.src;

                        return {
                            title: title.substring(0, 100),
                            uploader: uploader.substring(0, 50),
                            thumbnailUrl: thumbnailUrl,
                            resolution: { width: 0, height: 0 }
                        };
                    } catch (e) {
                        AriaController.logger.error('Bilibili metadata extraction error:', e);
                        return this.getDefaultMetadata();
                    }
                },

                getSafeFilename: (metadata) => {
                    const title = metadata.title || 'Bilibili Video';
                    const uploader = metadata.uploader || 'Unknown UP主';
                    const safeTitle = title.replace(/[\\/:*?"<>|]/g, '-').substring(0, 60);
                    const safeUploader = uploader.replace(/[\\/:*?"<>|]/g, '-').substring(0, 30);
                    return `[${safeUploader}] ${safeTitle}`;
                },

                getDefaultMetadata: () => ({
                    title: 'Bilibili Video',
                    uploader: 'Unknown UP主',
                    thumbnailUrl: null,
                    resolution: { width: 0, height: 0 }
                })
            }
        };

        // 智能自适应变速控制器 - 继承400.js的核心算法
        class AdaptiveSpeedController {
            constructor(session) {
                this.session = session;
                this.videoElement = session.videoElement;
                this.animationFrameId = null;
                this.lastBufferCheck = 0;
                this.config = {
                    HIGH_WATERMARK: ARIA_CONFIG.SPEED_HIGH_WATERMARK,
                    LOW_WATERMARK: ARIA_CONFIG.SPEED_LOW_WATERMARK,
                    SMOOTHING: ARIA_CONFIG.SPEED_SMOOTHING_FACTOR,
                    MAX_SPEED: ARIA_CONFIG.DEFAULT_MAX_SPEED,
                    NORMAL_SPEED: 1.0
                };

                // 停滞检测
                this.stallCount = 0;
                this.lastCurrentTime = 0;
                this.lastBufferedEnd = 0;
                this.lastStallCheck = 0;
            }

            start() {
                if (!this.videoElement || this.animationFrameId) return;

                AriaController.logger.info(`Adaptive speed control started for session ${this.session.id}`);
                this.monitor();
            }

            stop() {
                if (this.animationFrameId) {
                    cancelAnimationFrame(this.animationFrameId);
                    this.animationFrameId = null;

                    if (this.videoElement && !this.videoElement.paused) {
                        try {
                            this.videoElement.playbackRate = this.config.NORMAL_SPEED;
                        } catch (e) {
                            AriaController.logger.debug('Failed to reset playback rate:', e);
                        }
                    }

                    AriaController.logger.info(`Adaptive speed control stopped for session ${this.session.id}`);
                }
            }

            monitor = () => {
                if (!this.videoElement || this.videoElement.paused || this.session.state === 'complete') {
                    this.stop();
                    return;
                }

                const now = performance.now();

                // 速度调整逻辑
                if (now - this.lastBufferCheck > 100) {
                    this.lastBufferCheck = now;

                    try {
                        if (this.videoElement.buffered.length > 0) {
                            const bufferEnd = this.videoElement.buffered.end(this.videoElement.buffered.length - 1);
                            const bufferAhead = bufferEnd - this.videoElement.currentTime;
                            let targetRate;

                            if (bufferAhead <= this.config.LOW_WATERMARK) {
                                targetRate = this.config.NORMAL_SPEED;
                            } else if (bufferAhead >= this.config.HIGH_WATERMARK) {
                                targetRate = this.config.MAX_SPEED;
                            } else {
                                const bufferRange = this.config.HIGH_WATERMARK - this.config.LOW_WATERMARK;
                                const speedRange = this.config.MAX_SPEED - this.config.NORMAL_SPEED;
                                const bufferProgress = (bufferAhead - this.config.LOW_WATERMARK) / bufferRange;
                                targetRate = this.config.NORMAL_SPEED + (speedRange * bufferProgress);
                            }

                            const currentRate = this.videoElement.playbackRate;
                            const newRate = currentRate + (targetRate - currentRate) * this.config.SMOOTHING;

                            if (Math.abs(newRate - currentRate) > 0.01) {
                                this.videoElement.playbackRate = newRate;
                            }
                        }
                    } catch (e) {
                        AriaController.logger.debug('Speed control error:', e);
                    }
                }

                // 停滞检测
                if (now - this.lastStallCheck > ARIA_CONFIG.STALL_CHECK_INTERVAL) {
                    this.lastStallCheck = now;
                    this.checkForStall();
                }

                this.animationFrameId = requestAnimationFrame(this.monitor);
            }

            checkForStall() {
                if (!this.videoElement || this.session.state !== 'capturing' || this.videoElement.paused) {
                    return;
                }

                try {
                    const currentTime = this.videoElement.currentTime;

                    if (Math.abs(currentTime - this.lastCurrentTime) < 0.1) {
                        if (!this.videoElement.paused && this.videoElement.readyState >= 2) {
                            if (this.videoElement.buffered.length > 0) {
                                const bufferedEnd = this.videoElement.buffered.end(this.videoElement.buffered.length - 1);

                                if (bufferedEnd > currentTime + 1 || bufferedEnd === this.lastBufferedEnd) {
                                    this.stallCount++;

                                    if (this.stallCount >= ARIA_CONFIG.STALL_THRESHOLD) {
                                        this.handleStall();
                                    }
                                }
                            }
                        }
                    } else {
                        this.stallCount = 0;
                    }

                    this.lastCurrentTime = currentTime;
                    if (this.videoElement.buffered.length > 0) {
                        this.lastBufferedEnd = this.videoElement.buffered.end(this.videoElement.buffered.length - 1);
                    }
                } catch (e) {
                    AriaController.logger.error('Stall detection error:', e);
                }
            }

            handleStall() {
                AriaController.logger.info(`Stall detected for session ${this.session.id}`);
                this.session.markAsStalled();
                this.stop();
            }
        }

        // 捕获会话 - 核心媒体流捕获逻辑
        class CaptureSession {
            constructor(id, mediaSource, isResume = false) {
                this.id = id;
                this.mediaSource = mediaSource;
                this.state = 'capturing';
                this.videoElement = null;
                this.capturedSize = { video: 0, audio: 0 };
                this.startTime = Date.now();
                this.dbManager = AriaDownloader.getInstance().dbManager;
                this.speedController = null;
                this.preparedBlobs = { video: null, audio: null };
                this.isResume = isResume;
                this.chunkQueue = { video: [], audio: [] };
                this.processingQueue = false;
                this.fileExtensions = { video: '.webm', audio: '.webm' };
                this.metadata = null;
                this.resolution = { width: 0, height: 0 };

                if (isResume) {
                    this.loadResumeState();
                } else {
                    this.loadCurrentMetadata();
                }
            }

            loadResumeState() {
                try {
                    const controller = AriaDownloader.getInstance();
                    const activeSessions = controller.heartbeatManager.getActiveSessions();
                    const sessionData = activeSessions[this.id];

                    if (sessionData) {
                        if (sessionData.capturedSize) {
                            this.capturedSize = sessionData.capturedSize;
                        }
                        if (sessionData.startTime) {
                            this.startTime = sessionData.startTime;
                        }
                        if (sessionData.fileExtensions) {
                            this.fileExtensions = sessionData.fileExtensions;
                        }
                        if (sessionData.metadata) {
                            this.metadata = sessionData.metadata;
                        }
                        if (sessionData.resolution) {
                            this.resolution = sessionData.resolution;
                        }
                        AriaController.logger.info(`Resumed session ${this.id} with previous state`);
                    }
                } catch (e) {
                    AriaController.logger.error('Failed to load resume state:', e);
                }
            }

            loadCurrentMetadata() {
                try {
                    const controller = AriaDownloader.getInstance();
                    this.metadata = controller.getCurrentMetadata();
                    AriaController.logger.info(`Loaded metadata for session ${this.id}:`, this.metadata);
                } catch (e) {
                    AriaController.logger.error('Failed to load current metadata:', e);
                    this.metadata = {
                        title: 'Unknown Video',
                        uploader: 'Unknown',
                        thumbnailUrl: null,
                        resolution: { width: 0, height: 0 }
                    };
                }
            }

            setMimeType(type, mimeType) {
                const mimeToExt = {
                    'video/webm': '.webm', 'video/mp4': '.mp4', 'video/x-matroska': '.mkv',
                    'audio/webm': '.webm', 'audio/mp4': '.m4a', 'audio/mpeg': '.mp3', 'audio/ogg': '.ogg'
                };

                let ext = mimeToExt[mimeType] || mimeToExt[mimeType.split(';')[0].trim()];
                if (!ext) {
                    const mimeMatch = mimeType.match(/^[^/]+\/(.+)$/);
                    ext = mimeMatch ? '.' + mimeMatch[1] : (type === 'audio' ? '.webm' : '.webm');
                }

                this.fileExtensions[type] = ext;
                AriaController.logger.debug(`Set ${type} extension to ${ext}`);
            }

            async captureData(type, buffer) {
                const totalSizeMB = (this.capturedSize.video + this.capturedSize.audio) / 1024 / 1024;
                if (totalSizeMB >= ARIA_CONFIG.MAX_CAPTURE_SIZE_MB) {
                    AriaController.logger.warn(`Capture size limit reached: ${totalSizeMB}MB`);
                    this.complete();
                    return;
                }

                this.chunkQueue[type].push(buffer.slice(0));
                if (!this.processingQueue) {
                    this.processChunkQueue();
                }
            }

            async processChunkQueue() {
                this.processingQueue = true;
                try {
                    for (const type of ['video', 'audio']) {
                        while (this.chunkQueue[type].length > 0) {
                            const batch = this.chunkQueue[type].splice(0, ARIA_CONFIG.CHUNK_BATCH_SIZE);

                            for (const chunk of batch) {
                                let retries = 0;
                                while (retries < ARIA_CONFIG.RETRY_ATTEMPTS) {
                                    try {
                                        await this.dbManager.saveChunk(this.id, type, chunk);
                                        this.capturedSize[type] += chunk.byteLength;
                                        break;
                                    } catch (e) {
                                        retries++;
                                        if (e.message === 'QUOTA_EXCEEDED') {
                                            this.complete();
                                            return;
                                        }
                                        if (retries < ARIA_CONFIG.RETRY_ATTEMPTS) {
                                            await new Promise(resolve =>
                                                setTimeout(resolve, ARIA_CONFIG.RETRY_DELAY * retries)
                                            );
                                        } else {
                                            throw e;
                                        }
                                    }
                                }
                            }
                            AriaDownloader.getInstance().updateSessionHeartbeat(this.id, this.getHeartbeatData());
                        }
                    }
                } catch (e) {
                    this.state = 'error';
                    AriaController.logger.error(`Failed to save chunks for session ${this.id}:`, e);
                } finally {
                    this.processingQueue = false;
                    if (this.chunkQueue.video.length > 0 || this.chunkQueue.audio.length > 0) {
                        setTimeout(() => this.processChunkQueue(), 100);
                    }
                }
            }

            getHeartbeatData() {
                return {
                    state: this.state,
                    capturedSize: this.capturedSize,
                    startTime: this.startTime,
                    currentTime: this.videoElement?.currentTime || 0,
                    fileExtensions: this.fileExtensions,
                    metadata: this.metadata,
                    resolution: this.resolution
                };
            }

            markAsStalled() {
                this.state = 'stalled';
                AriaDownloader.getInstance().updateSessionHeartbeat(this.id, {
                    ...this.getHeartbeatData(),
                    stallTime: this.videoElement?.currentTime || 0,
                    state: 'stalled'
                });
                if (this.speedController) this.speedController.stop();
            }

            async complete() {
                if (this.state === 'complete') return;
                this.state = 'complete';
                if (this.speedController) this.speedController.stop();

                while (this.processingQueue || this.chunkQueue.video.length > 0 || this.chunkQueue.audio.length > 0) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                await this.prepareBlobUrls();
                AriaDownloader.getInstance().onSessionComplete(this);
            }

            async prepareBlobUrls() {
                try {
                    const videoChunks = await this.dbManager.getChunks(this.id, 'video');
                    if (videoChunks.length > 0) {
                        const videoBlob = new Blob(videoChunks.map(c => c.data), { type: 'video/webm' });
                        this.preparedBlobs.video = URL.createObjectURL(videoBlob);
                    }

                    const audioChunks = await this.dbManager.getChunks(this.id, 'audio');
                    if (audioChunks.length > 0) {
                        const audioBlob = new Blob(audioChunks.map(c => c.data), { type: 'audio/webm' });
                        this.preparedBlobs.audio = URL.createObjectURL(audioBlob);
                    }
                } catch (e) {
                    AriaController.logger.error(`Failed to prepare blob URLs for session ${this.id}:`, e);
                }
            }
        }

        // MediaSource劫持管理器 - 核心捕获引擎
        class HijackManager {
            constructor() {
                this.originalAddSourceBuffer = null;
                this.originalEndOfStream = null;
                this.originalAppendBuffer = null;
                this.mediaSourceSessions = new WeakMap();
                this.sourceBufferSessions = new WeakMap();
            }

            init() {
                this.hijackMediaSource();
                AriaController.logger.info('MediaSource hijacking initialized');
            }

            hijackMediaSource() {
                // 保存原始方法
                this.originalAddSourceBuffer = MediaSource.prototype.addSourceBuffer;
                this.originalEndOfStream = MediaSource.prototype.endOfStream;

                const hijackManager = this;

                // 劫持addSourceBuffer
                MediaSource.prototype.addSourceBuffer = function(mimeType) {
                    AriaController.logger.debug('MediaSource.addSourceBuffer called with:', mimeType);

                    const sourceBuffer = hijackManager.originalAddSourceBuffer.call(this, mimeType);

                    // 创建或获取会话
                    let session = hijackManager.mediaSourceSessions.get(this);
                    if (!session) {
                        const sessionId = hijackManager.generateSessionId();
                        session = new CaptureSession(sessionId, this);
                        hijackManager.mediaSourceSessions.set(this, session);
                        AriaController.getInstance().registerSession(session);
                    }

                    // 劫持appendBuffer
                    hijackManager.hijackSourceBuffer(sourceBuffer, session, mimeType);

                    return sourceBuffer;
                };

                // 劫持endOfStream
                MediaSource.prototype.endOfStream = function(endOfStreamError) {
                    AriaController.logger.debug('MediaSource.endOfStream called');

                    const session = hijackManager.mediaSourceSessions.get(this);
                    if (session) {
                        session.complete();
                    }

                    return hijackManager.originalEndOfStream.call(this, endOfStreamError);
                };
            }

            hijackSourceBuffer(sourceBuffer, session, mimeType) {
                // 确定媒体类型
                const type = mimeType.startsWith('video/') ? 'video' : 'audio';
                session.setMimeType(type, mimeType);

                // 保存原始appendBuffer方法
                const originalAppendBuffer = sourceBuffer.appendBuffer;

                // 劫持appendBuffer
                sourceBuffer.appendBuffer = function(buffer) {
                    try {
                        // 捕获数据
                        session.captureData(type, buffer);
                    } catch (e) {
                        AriaController.logger.error('Failed to capture data:', e);
                    }

                    // 调用原始方法
                    return originalAppendBuffer.call(this, buffer);
                };

                this.sourceBufferSessions.set(sourceBuffer, session);
            }

            generateSessionId() {
                const timestamp = Date.now();
                const random = Math.random().toString(36).substring(2, 8);
                return `aria_${timestamp}_${random}`;
            }
        }

        // GUI管理器 - Shadow DOM隔离UI
        class GUIManager {
            constructor() {
                this.shadowHost = null;
                this.shadowRoot = null;
                this.isVisible = false;
                this.updateInterval = null;
            }

            async init() {
                if (document.body) {
                    this.createGUI();
                } else {
                    document.addEventListener('DOMContentLoaded', () => this.createGUI());
                }
            }

            createGUI() {
                try {
                    // 创建Shadow DOM宿主
                    this.shadowHost = document.createElement('div');
                    this.shadowHost.id = 'aria-shadow-host';
                    this.shadowHost.style.cssText = `
                        position: fixed !important;
                        bottom: 20px !important;
                        right: 20px !important;
                        z-index: 2147483647 !important;
                        pointer-events: none !important;
                    `;

                    // 创建封闭的Shadow DOM
                    this.shadowRoot = this.shadowHost.attachShadow({ mode: 'closed' });

                    // 注入UI模板
                    this.shadowRoot.innerHTML = this.getUITemplate();

                    // 添加到页面
                    document.body.appendChild(this.shadowHost);

                    // 设置事件监听器
                    this.setupEventListeners();

                    // 开始状态更新
                    this.startStatusUpdates();

                    AriaController.logger.info('GUI created successfully');
                } catch (e) {
                    AriaController.logger.error('Failed to create GUI:', e);
                }
            }

            getUITemplate() {
                return `
                    <style>
                        * { box-sizing: border-box; margin: 0; padding: 0; }

                        .aria-container {
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            font-size: 13px;
                            line-height: 1.4;
                            pointer-events: auto;
                        }

                        .aria-toggle-btn {
                            width: 50px;
                            height: 50px;
                            border-radius: 50%;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            border: none;
                            color: white;
                            font-size: 20px;
                            cursor: pointer;
                            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                            transition: all 0.3s ease;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }

                        .aria-toggle-btn:hover {
                            transform: scale(1.1);
                            box-shadow: 0 6px 25px rgba(0,0,0,0.4);
                        }

                        .aria-panel {
                            position: absolute;
                            bottom: 60px;
                            right: 0;
                            width: 380px;
                            background: white;
                            border-radius: 12px;
                            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
                            border: 1px solid #e1e5e9;
                            display: none;
                            overflow: hidden;
                        }

                        .aria-header {
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 16px 20px;
                            font-weight: 600;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }

                        .aria-content {
                            padding: 20px;
                            max-height: 500px;
                            overflow-y: auto;
                        }

                        .aria-status {
                            background: #f8f9fa;
                            border-radius: 8px;
                            padding: 16px;
                            margin-bottom: 16px;
                        }

                        .aria-session {
                            border: 1px solid #e1e5e9;
                            border-radius: 8px;
                            padding: 16px;
                            margin-bottom: 12px;
                            background: white;
                        }

                        .aria-session.complete {
                            border-color: #28a745;
                            background: #f8fff9;
                        }

                        .aria-session.stalled {
                            border-color: #ffc107;
                            background: #fffdf5;
                        }

                        .aria-btn {
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 12px;
                            margin-right: 8px;
                            margin-top: 8px;
                            transition: background 0.2s;
                        }

                        .aria-btn:hover { background: #0056b3; }
                        .aria-btn.success { background: #28a745; }
                        .aria-btn.success:hover { background: #1e7e34; }
                        .aria-btn:disabled { background: #6c757d; cursor: not-allowed; }

                        .aria-progress {
                            width: 100%;
                            height: 6px;
                            background: #e9ecef;
                            border-radius: 3px;
                            margin: 8px 0;
                            overflow: hidden;
                        }

                        .aria-progress-bar {
                            height: 100%;
                            background: linear-gradient(90deg, #28a745, #20c997);
                            transition: width 0.3s ease;
                        }

                        .aria-settings {
                            border-top: 1px solid #e1e5e9;
                            padding: 16px 20px;
                            background: #f8f9fa;
                        }

                        .aria-setting-item {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 12px;
                        }

                        .aria-setting-item:last-child {
                            margin-bottom: 0;
                        }

                        .aria-switch {
                            position: relative;
                            width: 44px;
                            height: 24px;
                            background: #ccc;
                            border-radius: 12px;
                            cursor: pointer;
                            transition: background 0.3s;
                        }

                        .aria-switch.active {
                            background: #007bff;
                        }

                        .aria-switch::after {
                            content: '';
                            position: absolute;
                            top: 2px;
                            left: 2px;
                            width: 20px;
                            height: 20px;
                            background: white;
                            border-radius: 50%;
                            transition: transform 0.3s;
                        }

                        .aria-switch.active::after {
                            transform: translateX(20px);
                        }

                        .aria-input {
                            width: 60px;
                            padding: 4px 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            font-size: 12px;
                        }
                    </style>

                    <div class="aria-container">
                        <button class="aria-toggle-btn" id="aria-toggle">
                            📥
                        </button>

                        <div class="aria-panel" id="aria-panel">
                            <div class="aria-header">
                                <span>Aria Downloader v5.0.0</span>
                                <button class="aria-btn" id="aria-close" style="padding: 4px 8px;">×</button>
                            </div>

                            <div class="aria-content">
                                <div class="aria-status" id="aria-status">
                                    <div><strong>状态:</strong> <span id="status-text">等待媒体流...</span></div>
                                    <div><strong>活跃会话:</strong> <span id="session-count">0</span></div>
                                    <div><strong>存储使用:</strong> <span id="storage-usage">检查中...</span></div>
                                </div>

                                <div id="aria-sessions"></div>
                            </div>

                            <div class="aria-settings">
                                <div class="aria-setting-item">
                                    <label>智能变速</label>
                                    <div class="aria-switch" id="speed-toggle"></div>
                                </div>
                                <div class="aria-setting-item">
                                    <label>最大速度</label>
                                    <input type="number" class="aria-input" id="max-speed" min="1" max="16" step="0.5" value="8">
                                </div>
                                <div class="aria-setting-item">
                                    <label>自动下载</label>
                                    <div class="aria-switch" id="auto-download-toggle"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            setupEventListeners() {
                const toggleBtn = this.shadowRoot.getElementById('aria-toggle');
                const panel = this.shadowRoot.getElementById('aria-panel');
                const closeBtn = this.shadowRoot.getElementById('aria-close');
                const speedToggle = this.shadowRoot.getElementById('speed-toggle');
                const autoDownloadToggle = this.shadowRoot.getElementById('auto-download-toggle');
                const maxSpeedInput = this.shadowRoot.getElementById('max-speed');

                // 切换面板显示
                toggleBtn.addEventListener('click', () => {
                    this.isVisible = !this.isVisible;
                    panel.style.display = this.isVisible ? 'block' : 'none';
                });

                closeBtn.addEventListener('click', () => {
                    this.isVisible = false;
                    panel.style.display = 'none';
                });

                // 设置开关
                speedToggle.addEventListener('click', () => {
                    const controller = AriaDownloader.getInstance();
                    controller.settings.smartSpeed = !controller.settings.smartSpeed;
                    speedToggle.classList.toggle('active', controller.settings.smartSpeed);
                    controller.saveSettings();
                });

                autoDownloadToggle.addEventListener('click', () => {
                    const controller = AriaDownloader.getInstance();
                    controller.settings.autoDownload = !controller.settings.autoDownload;
                    autoDownloadToggle.classList.toggle('active', controller.settings.autoDownload);
                    controller.saveSettings();
                });

                maxSpeedInput.addEventListener('change', () => {
                    const controller = AriaDownloader.getInstance();
                    controller.settings.maxSpeed = parseFloat(maxSpeedInput.value) || 8.0;
                    controller.saveSettings();
                });

                // 下载按钮事件委托
                panel.addEventListener('click', (e) => {
                    if (e.target.hasAttribute('data-download-type')) {
                        const sessionId = e.target.getAttribute('data-session-id');
                        const downloadType = e.target.getAttribute('data-download-type');
                        this.handleDownloadClick(sessionId, downloadType);
                    }
                });

                // 初始化设置状态
                const controller = AriaDownloader.getInstance();
                speedToggle.classList.toggle('active', controller.settings.smartSpeed);
                autoDownloadToggle.classList.toggle('active', controller.settings.autoDownload);
                maxSpeedInput.value = controller.settings.maxSpeed;
            }

            startStatusUpdates() {
                this.updateInterval = setInterval(() => {
                    this.updateStatus();
                }, 1000);
            }

            updateStatus() {
                try {
                    const controller = AriaDownloader.getInstance();
                    const sessions = controller.activeSessions;

                    // 更新基本状态
                    const statusText = this.shadowRoot.getElementById('status-text');
                    const sessionCount = this.shadowRoot.getElementById('session-count');

                    if (sessions.size > 0) {
                        const capturingSessions = Array.from(sessions.values()).filter(s => s.state === 'capturing');
                        statusText.textContent = capturingSessions.length > 0 ? '正在捕获...' : '捕获完成';
                    } else {
                        statusText.textContent = '等待媒体流...';
                    }

                    sessionCount.textContent = sessions.size;

                    // 更新会话列表
                    this.updateSessionList();

                    // 更新存储使用情况
                    this.updateStorageUsage();
                } catch (e) {
                    AriaController.logger.error('Failed to update status:', e);
                }
            }

            async updateStorageUsage() {
                try {
                    const controller = AriaDownloader.getInstance();
                    const usage = await controller.dbManager.checkStorageUsage();
                    const storageElement = this.shadowRoot.getElementById('storage-usage');

                    if (usage) {
                        const usedMB = Math.round(usage.usage / 1024 / 1024);
                        const totalMB = Math.round(usage.quota / 1024 / 1024);
                        const percent = Math.round(usage.percent);
                        storageElement.textContent = `${usedMB}MB / ${totalMB}MB (${percent}%)`;

                        if (usage.percent > ARIA_CONFIG.STORAGE_WARNING_THRESHOLD * 100) {
                            storageElement.style.color = '#dc3545';
                        } else {
                            storageElement.style.color = '#28a745';
                        }
                    } else {
                        storageElement.textContent = '无法检测';
                    }
                } catch (e) {
                    AriaController.logger.error('Failed to update storage usage:', e);
                }
            }

            updateSessionList() {
                const sessionsContainer = this.shadowRoot.getElementById('aria-sessions');
                const controller = AriaDownloader.getInstance();

                // 清空现有内容
                sessionsContainer.innerHTML = '';

                // 添加每个会话
                for (const [sessionId, session] of controller.activeSessions) {
                    const sessionElement = this.createSessionElement(session);
                    sessionsContainer.appendChild(sessionElement);
                }
            }

            createSessionElement(session) {
                const div = document.createElement('div');
                div.className = `aria-session ${session.state}`;

                const videoSizeMB = Math.round(session.capturedSize.video / 1024 / 1024 * 100) / 100;
                const audioSizeMB = Math.round(session.capturedSize.audio / 1024 / 1024 * 100) / 100;
                const duration = Math.round((Date.now() - session.startTime) / 1000);

                const title = session.metadata?.title || 'Unknown Video';
                const uploader = session.metadata?.uploader || 'Unknown';

                div.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 8px;">${title}</div>
                    <div style="color: #6c757d; margin-bottom: 8px;">by ${uploader}</div>
                    <div style="margin-bottom: 8px;">
                        <span>视频: ${videoSizeMB}MB</span> |
                        <span>音频: ${audioSizeMB}MB</span> |
                        <span>时长: ${duration}s</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span>状态: ${this.getStateText(session.state)}</span>
                        ${session.videoElement ? ` | 速度: ${session.videoElement.playbackRate.toFixed(2)}x` : ''}
                    </div>
                    ${session.state === 'complete' ? this.createDownloadButtons(session) : ''}
                `;

                return div;
            }

            createDownloadButtons(session) {
                const buttons = [];

                if (session.preparedBlobs.video) {
                    buttons.push(`<button class="aria-btn success" data-download-type="video" data-session-id="${session.id}">下载视频</button>`);
                }

                if (session.preparedBlobs.audio) {
                    buttons.push(`<button class="aria-btn success" data-download-type="audio" data-session-id="${session.id}">下载音频</button>`);
                }

                return buttons.length > 0 ? `<div>${buttons.join('')}</div>` : '';
            }

            getFilename(session, type) {
                const extractor = AriaDownloader.getInstance().getCurrentExtractor();
                const baseFilename = extractor.getSafeFilename(session.metadata);
                const extension = session.fileExtensions[type];
                return `${baseFilename}_${type}${extension}`;
            }

            getStateText(state) {
                const stateTexts = {
                    'capturing': '捕获中',
                    'complete': '已完成',
                    'stalled': '已停滞',
                    'error': '错误'
                };
                return stateTexts[state] || state;
            }

            handleDownloadClick(sessionId, downloadType) {
                const controller = AriaDownloader.getInstance();
                const session = controller.activeSessions.get(sessionId);

                if (session && session.preparedBlobs[downloadType]) {
                    const filename = this.getFilename(session, downloadType);
                    this.downloadFile(session.preparedBlobs[downloadType], filename);
                }
            }

            downloadFile(url, filename) {
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                AriaController.logger.info(`Downloaded: ${filename}`);

                // 延迟释放URL
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, ARIA_CONFIG.BLOB_URL_REVOKE_DELAY);
            }
        }

        // 主控制器 - 系统核心协调器
        class AriaController {
            constructor() {
                this.activeSessions = new Map();
                this.dbManager = new DBManager();
                this.hijackManager = new HijackManager();
                this.guiManager = new GUIManager();
                this.heartbeatManager = new HeartbeatManager();
                this.settings = {
                    smartSpeed: true,
                    autoDownload: false,
                    maxSpeed: 8.0,
                    debugMode: false
                };
                this.cleanupInterval = null;
                this.heartbeatInterval = null;
            }

            static getInstance() {
                if (!AriaController.instance) {
                    AriaController.instance = new AriaController();
                }
                return AriaController.instance;
            }

            async init() {
                try {
                    AriaController.logger.info('Initializing Aria Controller v5.0.0');

                    // 加载设置
                    await this.loadSettings();

                    // 初始化数据库
                    await this.dbManager.init();

                    // 初始化劫持管理器
                    this.hijackManager.init();

                    // 初始化GUI
                    await this.guiManager.init();

                    // 恢复之前的会话
                    await this.resumePreviousSessions();

                    // 启动心跳和清理
                    this.startHeartbeat();
                    this.startCleanupTimer();

                    AriaController.logger.info('Aria Controller initialized successfully');
                } catch (e) {
                    AriaController.logger.error('Failed to initialize AriaController:', e);
                    throw e;
                }
            }

            async loadSettings() {
                try {
                    const savedSettings = GM_getValue('aria_settings_v5', null);
                    if (savedSettings) {
                        this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
                    }
                    AriaController.logger.debug('Settings loaded:', this.settings);
                } catch (e) {
                    AriaController.logger.error('Failed to load settings:', e);
                }
            }

            saveSettings() {
                try {
                    GM_setValue('aria_settings_v5', JSON.stringify(this.settings));
                    AriaController.logger.debug('Settings saved:', this.settings);
                } catch (e) {
                    AriaController.logger.error('Failed to save settings:', e);
                }
            }

            registerSession(session) {
                this.activeSessions.set(session.id, session);
                AriaController.logger.info(`Session registered: ${session.id}`);

                // 查找并关联视频元素
                this.findAndAttachVideoElement(session);

                // 更新心跳
                this.updateSessionHeartbeat(session.id, session.getHeartbeatData());
            }

            async findAndAttachVideoElement(session) {
                const maxAttempts = 20;
                let attempts = 0;

                const findVideo = () => {
                    attempts++;

                    // 查找视频元素
                    const videos = document.querySelectorAll('video');
                    for (const video of videos) {
                        if (video.readyState >= 1 && !video.dataset.ariaAttached) {
                            video.dataset.ariaAttached = 'true';
                            session.videoElement = video;

                            // 启动智能变速
                            if (this.settings.smartSpeed) {
                                session.speedController = new AdaptiveSpeedController(session);
                                session.speedController.start();
                            }

                            AriaController.logger.info(`Video element attached to session ${session.id}`);
                            return true;
                        }
                    }

                    if (attempts < maxAttempts) {
                        setTimeout(findVideo, 1000);
                    } else {
                        AriaController.logger.warn(`Failed to find video element for session ${session.id} after ${maxAttempts} attempts`);
                    }

                    return false;
                };

                findVideo();
            }

            getCurrentExtractor() {
                const url = window.location.href;

                if (SiteExtractors.YouTube.match(url)) {
                    return SiteExtractors.YouTube;
                } else if (SiteExtractors.Bilibili.match(url)) {
                    return SiteExtractors.Bilibili;
                } else {
                    // 返回通用提取器
                    return {
                        name: 'GenericExtractor',
                        extractMetadata: (doc) => ({
                            title: doc.title || 'Video',
                            uploader: new URL(window.location.href).hostname,
                            thumbnailUrl: null,
                            resolution: { width: 0, height: 0 }
                        }),
                        getSafeFilename: (metadata) => {
                            const title = metadata.title.replace(/[\\/:*?"<>|]/g, '-').substring(0, 50);
                            return title || 'video';
                        }
                    };
                }
            }

            getCurrentMetadata() {
                const extractor = this.getCurrentExtractor();
                return extractor.extractMetadata(document);
            }

            updateSessionHeartbeat(sessionId, data) {
                this.heartbeatManager.updateSession(sessionId, data);
            }

            async resumePreviousSessions() {
                try {
                    const activeSessions = this.heartbeatManager.getActiveSessions();
                    const dbSessionIds = await this.dbManager.getAllSessionIds();

                    for (const sessionId of dbSessionIds) {
                        if (activeSessions[sessionId] && !this.activeSessions.has(sessionId)) {
                            AriaController.logger.info(`Attempting to resume session: ${sessionId}`);
                            // 创建恢复会话的逻辑可以在这里实现
                        }
                    }
                } catch (e) {
                    AriaController.logger.error('Failed to resume previous sessions:', e);
                }
            }

            onSessionComplete(session) {
                AriaController.logger.info(`Session ${session.id} completed`);

                if (this.settings.autoDownload) {
                    this.autoDownloadSession(session);
                }

                // 显示完成通知
                this.showCompletionNotification(session);
            }

            autoDownloadSession(session) {
                setTimeout(() => {
                    if (session.preparedBlobs.video) {
                        this.guiManager.downloadFile(
                            session.preparedBlobs.video,
                            this.guiManager.getFilename(session, 'video')
                        );
                    }
                    if (session.preparedBlobs.audio) {
                        this.guiManager.downloadFile(
                            session.preparedBlobs.audio,
                            this.guiManager.getFilename(session, 'audio')
                        );
                    }
                }, 1000);
            }

            showCompletionNotification(session) {
                // 简单的通知实现
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: #28a745;
                    color: white;
                    padding: 12px 24px;
                    border-radius: 6px;
                    z-index: 2147483647;
                    font-family: Arial, sans-serif;
                    font-size: 14px;
                `;
                notification.textContent = `媒体捕获完成: ${session.metadata?.title || 'Unknown Video'}`;

                document.body.appendChild(notification);
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 5000);
            }

            startHeartbeat() {
                this.heartbeatInterval = setInterval(() => {
                    for (const [sessionId, session] of this.activeSessions) {
                        this.updateSessionHeartbeat(sessionId, session.getHeartbeatData());
                    }
                }, ARIA_CONFIG.HEARTBEAT_INTERVAL);
            }

            startCleanupTimer() {
                this.cleanupInterval = setInterval(() => {
                    this.performCleanup();
                }, ARIA_CONFIG.CLEANUP_INTERVAL);
            }

            async performCleanup() {
                try {
                    // 清理过期心跳
                    this.heartbeatManager.cleanupExpiredSessions();

                    // 检查存储使用情况
                    const usage = await this.dbManager.checkStorageUsage();
                    if (usage && usage.percent > ARIA_CONFIG.STORAGE_CRITICAL_THRESHOLD * 100) {
                        AriaController.logger.warn('Storage usage critical, performing cleanup');
                        await this.emergencyCleanup();
                    }
                } catch (e) {
                    AriaController.logger.error('Cleanup failed:', e);
                }
            }

            async emergencyCleanup() {
                try {
                    const allSessionIds = await this.dbManager.getAllSessionIds();
                    const activeSessions = this.heartbeatManager.getActiveSessions();

                    // 删除非活跃会话的数据
                    for (const sessionId of allSessionIds) {
                        if (!activeSessions[sessionId] && !this.activeSessions.has(sessionId)) {
                            await this.dbManager.deleteSession(sessionId);
                            AriaController.logger.info(`Cleaned up inactive session: ${sessionId}`);
                        }
                    }
                } catch (e) {
                    AriaController.logger.error('Emergency cleanup failed:', e);
                }
            }
        }

        // 单例实例
        let instance = null;

        return {
            getInstance: () => {
                if (!instance) {
                    instance = new AriaController();
                }
                return instance;
            },

            // 公开必要的类供外部使用
            Logger,
            DBManager,
            CaptureSession,
            AdaptiveSpeedController,
            HijackManager,
            GUIManager,
            HeartbeatManager,
            SiteExtractors
        };
    })();

    // 全局控制器引用
    const AriaController = AriaDownloader.getInstance();
    AriaController.logger = new AriaDownloader.Logger();

    // 主初始化函数
    async function initializeAria() {
        try {
            AriaController.logger.info('Starting Aria Downloader v5.0.0 Ultimate Refactor');

            // 等待DOM准备就绪
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 礼貌延迟，让页面稳定
            await new Promise(resolve => setTimeout(resolve, ARIA_CONFIG.ACTIVATION_DELAY));

            // 初始化控制器
            await AriaController.init();

            AriaController.logger.info('Aria Downloader v5.0.0 Ultimate Refactor initialized successfully');

            // 调试接口
            if (AriaController.settings.debugMode) {
                window.AriaDebug = {
                    controller: AriaController,
                    sessions: AriaController.activeSessions,
                    config: ARIA_CONFIG,
                    extractors: AriaDownloader.SiteExtractors
                };
                AriaController.logger.info('Debug interface available at window.AriaDebug');
            }

        } catch (error) {
            console.error('[Aria FATAL] Initialization failed:', error);

            // 尝试恢复
            try {
                AriaController.logger.error('Attempting emergency recovery...');
                await AriaController.dbManager.destroyDatabase();
                AriaController.logger.info('Database reset. Please refresh the page.');
            } catch (recoveryError) {
                console.error('[Aria FATAL] Recovery failed:', recoveryError);
            }
        }
    }

    // 启动系统
    initializeAria();

})();