// ==UserScript==
// @name         Aria Downloader Pro - Firefox Optimized
// @namespace    aria-downloader-firefox.io
// @version      5.0.0
// @description  Firefox优化版媒体捕获工具，模块化架构，增强性能和稳定性
// <AUTHOR> Project (Firefox Edition)
// @match        *://*.youtube.com/*
// @match        *://*.bilibili.com/*
// @match        *://*.vimeo.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_deleteValue
// @grant        GM_listValues
// @grant        GM_xmlhttpRequest
// @grant        unsafeWindow
// @run-at       document-start
// @require      https://cdn.jsdelivr.net/npm/dexie@3/dist/dexie.min.js
// ==/UserScript==

(function() {
    'use strict';
    
    // Firefox性能优化配置
    const FIREFOX_CONFIG = {
        maxConcurrentDownloads: 3,
        chunkSize: 1024 * 1024,
        retryAttempts: 3,
        timeout: 30000,
        memoryThreshold: 100 * 1024 * 1024
    };

    // 核心模块定义
    const AriaCore = {
        version: '5.0.0',
        platform: 'firefox',
        
        init() {
            console.log(`[Aria] Firefox版本 ${this.version} 初始化中...`);
            this.setupErrorHandling();
            this.initializeModules();
        },
        
        setupErrorHandling() {
            window.addEventListener('error', (event) => {
                console.error('[Aria] 全局错误:', event.error);
                this.reportError(event.error);
            });
            
            window.addEventListener('unhandledrejection', (event) => {
                console.error('[Aria] 未处理的Promise拒绝:', event.reason);
                this.reportError(event.reason);
            });
        },
        
        reportError(error) {
            try {
                GM_setValue('lastError', {
                    message: error.message,
                    stack: error.stack,
                    timestamp: Date.now()
                });
            } catch (e) {
                console.error('[Aria] 错误报告失败:', e);
            }
        },
        
        initializeModules() {
            try {
                MediaDetector.init();
                DownloadManager.init();
                UIManager.init();
                StorageManager.init();
            } catch (error) {
                console.error('[Aria] 模块初始化失败:', error);
                this.reportError(error);
            }
        }
    };

    // 媒体检测模块
    const MediaDetector = {
        patterns: {
            youtube: /youtube\.com\/watch\?v=([^&]+)/,
            bilibili: /bilibili\.com\/video\/([^/?]+)/,
            vimeo: /vimeo\.com\/(\d+)/
        },
        
        init() {
            console.log('[Aria] 媒体检测器初始化');
            this.setupObservers();
            this.detectCurrentMedia();
        },
        
        setupObservers() {
            // 监听页面变化
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        this.scanForMedia(mutation.target);
                    }
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        },
        
        detectCurrentMedia() {
            const url = window.location.href;
            for (const [platform, pattern] of Object.entries(this.patterns)) {
                const match = url.match(pattern);
                if (match) {
                    console.log(`[Aria] 检测到${platform}媒体:`, match[1]);
                    this.handleMediaDetection(platform, match[1]);
                    break;
                }
            }
        },
        
        scanForMedia(element) {
            // 扫描元素中的媒体内容
            const videos = element.querySelectorAll('video');
            const audios = element.querySelectorAll('audio');
            
            [...videos, ...audios].forEach(media => {
                if (media.src && !media.dataset.ariaProcessed) {
                    media.dataset.ariaProcessed = 'true';
                    this.processMediaElement(media);
                }
            });
        },
        
        processMediaElement(media) {
            console.log('[Aria] 处理媒体元素:', media.src);
            UIManager.addDownloadButton(media);
        },
        
        handleMediaDetection(platform, id) {
            UIManager.showMediaInfo(platform, id);
        }
    };

    // 下载管理器
    const DownloadManager = {
        activeDownloads: new Map(),
        
        init() {
            console.log('[Aria] 下载管理器初始化');
            this.setupDownloadQueue();
        },
        
        setupDownloadQueue() {
            this.downloadQueue = [];
            this.processing = false;
        },
        
        async startDownload(url, filename, options = {}) {
            const downloadId = this.generateDownloadId();
            
            try {
                console.log(`[Aria] 开始下载: ${filename}`);
                
                const download = {
                    id: downloadId,
                    url: url,
                    filename: filename,
                    status: 'downloading',
                    progress: 0,
                    startTime: Date.now(),
                    ...options
                };
                
                this.activeDownloads.set(downloadId, download);
                UIManager.updateDownloadProgress(downloadId, 0);
                
                await this.performDownload(download);
                
            } catch (error) {
                console.error('[Aria] 下载失败:', error);
                this.handleDownloadError(downloadId, error);
            }
        },
        
        async performDownload(download) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: download.url,
                    responseType: 'blob',
                    timeout: FIREFOX_CONFIG.timeout,
                    
                    onprogress: (progress) => {
                        if (progress.lengthComputable) {
                            const percent = (progress.loaded / progress.total) * 100;
                            download.progress = percent;
                            UIManager.updateDownloadProgress(download.id, percent);
                        }
                    },
                    
                    onload: (response) => {
                        if (response.status === 200) {
                            this.saveFile(download.filename, response.response);
                            this.completeDownload(download.id);
                            resolve();
                        } else {
                            reject(new Error(`HTTP ${response.status}`));
                        }
                    },
                    
                    onerror: (error) => {
                        reject(error);
                    },
                    
                    ontimeout: () => {
                        reject(new Error('下载超时'));
                    }
                });
            });
        },
        
        saveFile(filename, blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        },
        
        completeDownload(downloadId) {
            const download = this.activeDownloads.get(downloadId);
            if (download) {
                download.status = 'completed';
                download.endTime = Date.now();
                console.log(`[Aria] 下载完成: ${download.filename}`);
                UIManager.updateDownloadStatus(downloadId, 'completed');
            }
        },
        
        handleDownloadError(downloadId, error) {
            const download = this.activeDownloads.get(downloadId);
            if (download) {
                download.status = 'error';
                download.error = error.message;
                console.error(`[Aria] 下载错误: ${download.filename}`, error);
                UIManager.updateDownloadStatus(downloadId, 'error');
            }
        },
        
        generateDownloadId() {
            return 'download_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
    };

    // UI管理器
    const UIManager = {
        init() {
            console.log('[Aria] UI管理器初始化');
            this.createMainUI();
            this.setupStyles();
        },
        
        createMainUI() {
            // 创建主界面容器
            this.container = document.createElement('div');
            this.container.id = 'aria-downloader-container';
            this.container.innerHTML = `
                <div class="aria-header">
                    <h3>Aria Downloader Pro</h3>
                    <button class="aria-toggle">−</button>
                </div>
                <div class="aria-content">
                    <div class="aria-downloads"></div>
                </div>
            `;
            
            document.body.appendChild(this.container);
            this.setupEventListeners();
        },
        
        setupStyles() {
            GM_addStyle(`
                #aria-downloader-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 300px;
                    background: #fff;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 10000;
                    font-family: Arial, sans-serif;
                }
                
                .aria-header {
                    background: #4CAF50;
                    color: white;
                    padding: 10px 15px;
                    border-radius: 8px 8px 0 0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .aria-header h3 {
                    margin: 0;
                    font-size: 14px;
                }
                
                .aria-toggle {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                }
                
                .aria-content {
                    padding: 15px;
                    max-height: 400px;
                    overflow-y: auto;
                }
                
                .aria-download-item {
                    margin-bottom: 10px;
                    padding: 8px;
                    background: #f9f9f9;
                    border-radius: 4px;
                    font-size: 12px;
                }
                
                .aria-progress {
                    width: 100%;
                    height: 4px;
                    background: #eee;
                    border-radius: 2px;
                    margin-top: 5px;
                }
                
                .aria-progress-bar {
                    height: 100%;
                    background: #4CAF50;
                    border-radius: 2px;
                    transition: width 0.3s ease;
                }
                
                .aria-download-btn {
                    background: #4CAF50;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 11px;
                    margin-left: 5px;
                }
                
                .aria-download-btn:hover {
                    background: #45a049;
                }
            `);
        },
        
        setupEventListeners() {
            const toggle = this.container.querySelector('.aria-toggle');
            const content = this.container.querySelector('.aria-content');
            
            toggle.addEventListener('click', () => {
                const isHidden = content.style.display === 'none';
                content.style.display = isHidden ? 'block' : 'none';
                toggle.textContent = isHidden ? '−' : '+';
            });
        },
        
        addDownloadButton(mediaElement) {
            if (mediaElement.nextElementSibling?.classList.contains('aria-download-btn')) {
                return; // 按钮已存在
            }
            
            const button = document.createElement('button');
            button.className = 'aria-download-btn';
            button.textContent = '下载';
            button.onclick = () => {
                const filename = this.generateFilename(mediaElement.src);
                DownloadManager.startDownload(mediaElement.src, filename);
            };
            
            mediaElement.parentNode.insertBefore(button, mediaElement.nextSibling);
        },
        
        showMediaInfo(platform, id) {
            const info = document.createElement('div');
            info.className = 'aria-media-info';
            info.innerHTML = `
                <div>检测到${platform}媒体: ${id}</div>
                <button class="aria-download-btn" onclick="UIManager.downloadMedia('${platform}', '${id}')">
                    下载
                </button>
            `;
            
            const content = this.container.querySelector('.aria-content');
            content.appendChild(info);
        },
        
        updateDownloadProgress(downloadId, progress) {
            const item = document.getElementById(`download-${downloadId}`);
            if (!item) {
                this.createDownloadItem(downloadId, progress);
            } else {
                const progressBar = item.querySelector('.aria-progress-bar');
                if (progressBar) {
                    progressBar.style.width = `${progress}%`;
                }
            }
        },
        
        createDownloadItem(downloadId, progress) {
            const download = DownloadManager.activeDownloads.get(downloadId);
            if (!download) return;
            
            const item = document.createElement('div');
            item.id = `download-${downloadId}`;
            item.className = 'aria-download-item';
            item.innerHTML = `
                <div>${download.filename}</div>
                <div class="aria-progress">
                    <div class="aria-progress-bar" style="width: ${progress}%"></div>
                </div>
                <div class="aria-status">下载中...</div>
            `;
            
            const content = this.container.querySelector('.aria-downloads');
            content.appendChild(item);
        },
        
        updateDownloadStatus(downloadId, status) {
            const item = document.getElementById(`download-${downloadId}`);
            if (item) {
                const statusElement = item.querySelector('.aria-status');
                if (statusElement) {
                    statusElement.textContent = status === 'completed' ? '已完成' : '错误';
                    statusElement.style.color = status === 'completed' ? '#4CAF50' : '#f44336';
                }
            }
        },
        
        generateFilename(url) {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            const filename = pathname.split('/').pop() || 'download';
            return filename.includes('.') ? filename : `${filename}.mp4`;
        },
        
        downloadMedia(platform, id) {
            console.log(`[Aria] 请求下载${platform}媒体:`, id);
            // 这里可以添加特定平台的下载逻辑
        }
    };

    // 存储管理器
    const StorageManager = {
        init() {
            console.log('[Aria] 存储管理器初始化');
            this.setupDatabase();
        },
        
        setupDatabase() {
            try {
                this.db = new Dexie('AriaDownloader');
                this.db.version(1).stores({
                    downloads: '++id, url, filename, status, timestamp',
                    settings: 'key, value'
                });
                this.db.open();
            } catch (error) {
                console.error('[Aria] 数据库初始化失败:', error);
            }
        },
        
        async saveDownload(download) {
            try {
                await this.db.downloads.add({
                    ...download,
                    timestamp: Date.now()
                });
            } catch (error) {
                console.error('[Aria] 保存下载记录失败:', error);
            }
        },
        
        async getDownloadHistory() {
            try {
                return await this.db.downloads.orderBy('timestamp').reverse().toArray();
            } catch (error) {
                console.error('[Aria] 获取下载历史失败:', error);
                return [];
            }
        },
        
        async saveSetting(key, value) {
            try {
                await this.db.settings.put({ key, value });
            } catch (error) {
                console.error('[Aria] 保存设置失败:', error);
            }
        },
        
        async getSetting(key, defaultValue = null) {
            try {
                const setting = await this.db.settings.get(key);
                return setting ? setting.value : defaultValue;
            } catch (error) {
                console.error('[Aria] 获取设置失败:', error);
                return defaultValue;
            }
        }
    };

    // 启动器
    const AriaBootstrapper = {
        start() {
            console.log('[Aria] Firefox版启动器开始执行');
            
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    this.initialize();
                });
            } else {
                this.initialize();
            }
        },
        
        initialize() {
            try {
                AriaCore.init();
                console.log('[Aria] Firefox版初始化完成');
            } catch (error) {
                console.error('[Aria] 初始化失败:', error);
            }
        }
    };

    // 启动应用
    AriaBootstrapper.start();
})();