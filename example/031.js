// ==UserScript==
// @name         Unlimited_downloader_with_Persistent_Smart_AutoSpeed
// @name:zh-CN   无限制下载器 (持久智能自动变速版)
// @namespace    ooooooooo.io
// @version      0.3.1
// @description  Get video and audio binary streams directly. Features persistent, buffer-aware automatic playback speed adjustment for faster, smoother downloads.
// @description:zh-CN 直接获取视频和音频二进制流，打破所有下载限制。集成持久化、基于缓冲区的智能自动无级变速功能，优化下载效率与播放体验。
// <AUTHOR> (modified by AI)
// @match        *://*/*
// @exclude      https://mail.qq.com/*
// @exclude      https://wx.mail.qq.com/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function () {
  "use strict";
  const SCRIPT_VERSION = "0.3.1"; // Updated version
  console.log(
    `Unlimited_downloader_with_Persistent_Smart_AutoSpeed v${SCRIPT_VERSION}: begin......${location.href}`,
  );

  // Setting it to 1 will automatically download the video after it finishes playing.
  window.autoDownload = 1;

  window.isComplete = 0;
  window.audio = [];
  window.video = [];
  window.downloadAll = 0;

  // --- BEGIN: Persistent Smart Auto-Speed Feature ---
  const AUTO_SPEED_CONFIG = {
    enabled: true,
    minRate: 1.0,
    maxRate: 16.0,
    proactiveIncreaseIntervalMs: 7000,
    increaseFactor: 2.0,
    bufferMonitorIntervalMs: 1500,
    targetBufferHighSec: 25,
    targetBufferOptimalSec: 15,
    targetBufferLowSec: 8,
    criticalBufferLowSec: 3,
    decreaseFactor: 2.0,
    stallDecreaseFactor: 2.5,
    stallCooldownMs: 4000,
    initialGlobalDelayMs: 2500, // Initial delay before anything starts
    videoFindRetryIntervalMs: 3000, // How often to retry finding video if none suitable initially
    mutationObserverDebounceMs: 500, // Debounce for re-evaluating video on DOM changes
  };

  let currentVideoElement = null;
  let currentTargetPlaybackRate = AUTO_SPEED_CONFIG.minRate;
  let proactiveSpeedIncreaseTimer = null;
  let bufferMonitorTimer = null;
  let isVideoStalled = false;
  let lastStallTimestamp = 0;
  let lastRateChangeByScript = 0;

  // State for the persistent video finder
  let videoFinderState = {
    observer: null,
    isLogicActive: false, // Is auto-speed logic (timers, listeners) currently active for a video?
    lastKnownVideoSrc: null,
    observerDebounceTimeout: null,
    initialFindTimeout: null,
  };

  function logAutoSpeed(level, message) {
    const prefix = `[PersistentSmartAutoSpeed v${SCRIPT_VERSION}]`;
    if (level === "error") console.error(`${prefix} ${message}`);
    else if (level === "warn") console.warn(`${prefix} ${message}`);
    else console.log(`${prefix} ${message}`);
  }

  // --- Video Selection Heuristic ---
  function selectBestVideoElement() {
    let videos = Array.from(document.querySelectorAll("video")).filter(
      (v) => v.src || v.currentSrc,
    ); // Must have a source
    if (videos.length === 0) return null;

    videos.forEach((v) => {
      v._fitnessScore = 0;
      // Prioritize playing videos heavily
      if (!v.paused && v.currentTime > 0) v._fitnessScore += 200;
      // Visible and of reasonable size
      if (
        v.offsetWidth > 100 &&
        v.offsetHeight > 100 &&
        v.style.display !== "none" &&
        v.style.visibility !== "hidden"
      ) {
        v._fitnessScore += 100;
        v._fitnessScore += (v.offsetWidth * v.offsetHeight) / 10000; // Area bonus, scaled down
      }
      // Has duration (implies metadata loaded)
      if (v.duration > 1) v._fitnessScore += 50;
      // Ready state
      if (v.readyState >= 2) v._fitnessScore += 30; // HAVE_CURRENT_DATA or more
      // Penalize very short videos or those likely ads
      if (v.duration > 1 && v.duration < 15 && v._fitnessScore > 50)
        v._fitnessScore -= 40; // Potential ad
      if (v.offsetWidth < 50 || v.offsetHeight < 50) v._fitnessScore -= 100; // Too small
    });

    videos.sort((a, b) => b._fitnessScore - a._fitnessScore);

    logAutoSpeed(
      "info",
      `Video Candidates (Scores): ${videos.map((v) => `${(v.id || v.src || "video").substring(0, 30)}:${v._fitnessScore.toFixed(0)}`).join(", ")}`,
    );

    if (videos.length > 0 && videos[0]._fitnessScore > 20) {
      // Must have some minimal fitness
      return videos[0];
    }
    return null;
  }

  function stopAllAutoSpeedOperations(reason) {
    logAutoSpeed(
      "info",
      `Stopping all auto-speed operations. Reason: ${reason}`,
    );
    if (proactiveSpeedIncreaseTimer) clearInterval(proactiveSpeedIncreaseTimer);
    if (bufferMonitorTimer) clearInterval(bufferMonitorTimer);
    proactiveSpeedIncreaseTimer = null;
    bufferMonitorTimer = null;

    if (currentVideoElement) {
      currentVideoElement.removeEventListener(
        "waiting",
        handleVideoEvent_Waiting,
      );
      currentVideoElement.removeEventListener(
        "playing",
        handleVideoEvent_Playing,
      );
      currentVideoElement.removeEventListener("ended", handleVideoEvent_Ended);
      currentVideoElement.removeEventListener(
        "ratechange",
        handleVideoEvent_RateChange,
      );
      currentVideoElement.removeEventListener(
        "loadedmetadata",
        handleVideoMetadataLoaded,
      ); // Ensure this is cleaned up
      currentVideoElement.removeEventListener("emptied", handleVideoEmptied);
      currentVideoElement.removeEventListener("abort", handleVideoAbort);
      logAutoSpeed(
        "debug",
        `Event listeners removed from ${currentVideoElement.src || "previous video"}`,
      );
    }
    currentVideoElement = null;
    videoFinderState.isLogicActive = false;
    videoFinderState.lastKnownVideoSrc = null;
    // Do NOT stop the MutationObserver here, it should keep running.
  }

  // Placeholder for event handlers (most are unchanged from v0.3.0)
  function getVideoBufferInfo() {
    if (!currentVideoElement || currentVideoElement.readyState < 1) {
      // HAVE_METADATA or more
      return { currentBufferTime: 0, duration: 0, hasBuffer: false };
    }
    const buffered = currentVideoElement.buffered;
    const currentTime = currentVideoElement.currentTime;
    let currentBufferTime = 0;
    let hasBuffer = false;

    if (buffered && buffered.length > 0) {
      for (let i = buffered.length - 1; i >= 0; i--) {
        // Iterate backwards for latest buffer
        if (
          buffered.start(i) <= currentTime &&
          buffered.end(i) >= currentTime
        ) {
          currentBufferTime = buffered.end(i) - currentTime;
          hasBuffer = true;
          break;
        }
      }
      if (!hasBuffer) {
        // If currentTime is not in any range (e.g. after seeking to unbuffered)
        const lastBufferEnd =
          buffered.length > 0 ? buffered.end(buffered.length - 1) : 0;
        if (lastBufferEnd > currentTime) {
          // Is there any buffer ahead at all?
          currentBufferTime = lastBufferEnd - currentTime;
          hasBuffer = true;
        }
      }
    }
    return {
      currentBufferTime: Math.max(0, currentBufferTime), // Ensure non-negative
      duration: currentVideoElement.duration || 0,
      hasBuffer: hasBuffer,
    };
  }
  function setVideoPlaybackRate(newRate, reason = "unknown") {
    if (
      !currentVideoElement ||
      typeof currentVideoElement.playbackRate !== "number" ||
      !videoFinderState.isLogicActive
    ) {
      // logAutoSpeed('warn', `Cannot set playback rate: No valid video element or logic not active.`);
      return;
    }
    const clampedRate = Math.max(
      AUTO_SPEED_CONFIG.minRate,
      Math.min(newRate, AUTO_SPEED_CONFIG.maxRate),
    );
    if (Math.abs(currentVideoElement.playbackRate - clampedRate) > 0.01) {
      logAutoSpeed(
        "info",
        `Setting playback rate to: ${clampedRate.toFixed(2)}x (Reason: ${reason}, Current actual: ${currentVideoElement.playbackRate.toFixed(2)}x)`,
      );
      try {
        currentVideoElement.playbackRate = clampedRate;
        currentTargetPlaybackRate = clampedRate;
        lastRateChangeByScript = Date.now();
      } catch (e) {
        logAutoSpeed("error", `Error setting playbackRate: ${e.message}`);
      }
    } else if (currentTargetPlaybackRate !== clampedRate) {
      currentTargetPlaybackRate = clampedRate;
    }
  }
  function attemptSpeedIncrease(reason) {
    if (
      !videoFinderState.isLogicActive ||
      !currentVideoElement ||
      currentVideoElement.paused ||
      currentVideoElement.ended ||
      isVideoStalled
    )
      return false;
    if (Date.now() - lastStallTimestamp < AUTO_SPEED_CONFIG.stallCooldownMs) {
      /*logAutoSpeed('info', `Speed Increase Hold: In stall cooldown.`);*/ return false;
    }
    if (currentTargetPlaybackRate >= AUTO_SPEED_CONFIG.maxRate) return false;
    const newRate =
      currentTargetPlaybackRate * AUTO_SPEED_CONFIG.increaseFactor;
    setVideoPlaybackRate(newRate, `increase (${reason})`);
    return true;
  }
  function attemptSpeedDecrease(reason, customFactor) {
    if (!videoFinderState.isLogicActive || !currentVideoElement) return false;
    if (
      currentTargetPlaybackRate <= AUTO_SPEED_CONFIG.minRate &&
      reason !== "stall_critical" &&
      reason !== "critical_buffer_low_force_min"
    )
      return false;
    const factor = customFactor || AUTO_SPEED_CONFIG.decreaseFactor;
    const newRate = currentTargetPlaybackRate / factor;
    setVideoPlaybackRate(newRate, `decrease (${reason})`);
    return true;
  }
  function monitorBufferAndAdjust() {
    if (
      !videoFinderState.isLogicActive ||
      !currentVideoElement ||
      currentVideoElement.paused ||
      currentVideoElement.ended ||
      isVideoStalled
    )
      return;
    const { currentBufferTime, hasBuffer } = getVideoBufferInfo();
    // if (!hasBuffer && currentVideoElement.currentTime > 0.1 && currentVideoElement.networkState === currentVideoElement.NETWORK_LOADING) {
    //     logAutoSpeed('warn', `No buffer data, but video seems to be loading (currentTime: ${currentVideoElement.currentTime.toFixed(1)}s).`);
    // }
    if (
      currentBufferTime < AUTO_SPEED_CONFIG.criticalBufferLowSec &&
      currentTargetPlaybackRate > AUTO_SPEED_CONFIG.minRate
    ) {
      logAutoSpeed(
        "warn",
        `Buffer critically low (${currentBufferTime.toFixed(1)}s). Forcing significant speed decrease.`,
      );
      setVideoPlaybackRate(
        AUTO_SPEED_CONFIG.minRate,
        "critical_buffer_low_force_min",
      );
      isVideoStalled = true;
      lastStallTimestamp = Date.now();
    } else if (
      currentBufferTime < AUTO_SPEED_CONFIG.targetBufferLowSec &&
      currentTargetPlaybackRate > AUTO_SPEED_CONFIG.minRate
    ) {
      logAutoSpeed(
        "info",
        `Buffer low (${currentBufferTime.toFixed(1)}s). Preemptive speed decrease.`,
      );
      attemptSpeedDecrease("buffer_low_preemptive");
    }
  }
  function proactiveIncreaseVideoSpeed() {
    if (
      !videoFinderState.isLogicActive ||
      !currentVideoElement ||
      currentVideoElement.paused ||
      currentVideoElement.ended ||
      isVideoStalled
    )
      return;
    const { currentBufferTime } = getVideoBufferInfo();
    if (currentBufferTime > AUTO_SPEED_CONFIG.targetBufferHighSec) {
      // logAutoSpeed('info', `Buffer high (${currentBufferTime.toFixed(1)}s). Attempting proactive speed increase.`);
      attemptSpeedIncrease("buffer_high_proactive");
    }
  }
  function handleVideoEvent_Waiting() {
    if (!videoFinderState.isLogicActive || !currentVideoElement) return;
    logAutoSpeed("warn", "Video event: waiting (STALLED/BUFFERING)");
    isVideoStalled = true;
    lastStallTimestamp = Date.now();
    attemptSpeedDecrease("stall_event", AUTO_SPEED_CONFIG.stallDecreaseFactor);
  }
  function handleVideoEvent_Playing() {
    if (!videoFinderState.isLogicActive || !currentVideoElement) return;
    if (isVideoStalled)
      logAutoSpeed("info", "Video RESUMED playing after stall state.");
    isVideoStalled = false;
  }
  function handleVideoEvent_Ended() {
    // This should trigger a full stop and re-evaluation
    if (!videoFinderState.isLogicActive || !currentVideoElement) return;
    logAutoSpeed(
      "info",
      `Video event: ended (${currentVideoElement.src || "video"}). Auto-speed will stop for this video and re-evaluate.`,
    );
    stopAllAutoSpeedOperations("video_ended");
    // Trigger a new search for a video immediately, as another might start (e.g. playlist)
    evaluateAndManageVideoElement();
  }
  function handleVideoEvent_RateChange() {
    if (
      !videoFinderState.isLogicActive ||
      !currentVideoElement ||
      Date.now() - lastRateChangeByScript < 300
    )
      return;
    if (
      Math.abs(currentVideoElement.playbackRate - currentTargetPlaybackRate) >
      0.05
    ) {
      logAutoSpeed(
        "warn",
        `Playback rate changed EXTERNALLY to ${currentVideoElement.playbackRate.toFixed(2)}x (Script target was ${currentTargetPlaybackRate.toFixed(2)}x).`,
      );
      currentTargetPlaybackRate = Math.max(
        AUTO_SPEED_CONFIG.minRate,
        Math.min(currentVideoElement.playbackRate, AUTO_SPEED_CONFIG.maxRate),
      );
      logAutoSpeed(
        "info",
        `Adapted internal target rate to ${currentTargetPlaybackRate.toFixed(2)}x.`,
      );
    }
  }
  // New event handlers to detect video invalidation
  function handleVideoMetadataLoaded() {
    if (!currentVideoElement) return;
    logAutoSpeed(
      "info",
      `Video metadata loaded. Duration: ${currentVideoElement.duration ? currentVideoElement.duration.toFixed(1) : "N/A"}. Src: ${currentVideoElement.currentSrc}`,
    );
    // This might be a good point to confirm this is still the video we want.
    // The main loop will handle it if src changed.
  }
  function handleVideoEmptied() {
    // Fired when video becomes empty e.g. src change or error
    if (!currentVideoElement) return;
    logAutoSpeed(
      "warn",
      `Video event: emptied (${currentVideoElement.src || "video"}). May indicate src change or error. Re-evaluating.`,
    );
    stopAllAutoSpeedOperations("video_emptied");
    evaluateAndManageVideoElement(); // Trigger re-evaluation
  }
  function handleVideoAbort() {
    // Fired when video loading is aborted
    if (!currentVideoElement) return;
    logAutoSpeed(
      "warn",
      `Video event: abort (${currentVideoElement.src || "video"}). Loading aborted. Re-evaluating.`,
    );
    stopAllAutoSpeedOperations("video_aborted");
    evaluateAndManageVideoElement();
  }

  function initializeAutoSpeedLogicForVideo(videoEl) {
    if (videoFinderState.isLogicActive && currentVideoElement === videoEl) {
      logAutoSpeed(
        "debug",
        "Logic already active for this exact video element.",
      );
      return; // Already active for this exact element
    }
    stopAllAutoSpeedOperations("new_video_initialization"); // Clean up any previous state

    currentVideoElement = videoEl;
    videoFinderState.lastKnownVideoSrc =
      currentVideoElement.currentSrc || currentVideoElement.src;
    logAutoSpeed(
      "info",
      `Initializing smart auto-speed for video: ${videoFinderState.lastKnownVideoSrc}`,
    );

    currentVideoElement.addEventListener("waiting", handleVideoEvent_Waiting);
    currentVideoElement.addEventListener("playing", handleVideoEvent_Playing);
    currentVideoElement.addEventListener("ended", handleVideoEvent_Ended);
    currentVideoElement.addEventListener(
      "ratechange",
      handleVideoEvent_RateChange,
    );
    currentVideoElement.addEventListener(
      "loadedmetadata",
      handleVideoMetadataLoaded,
    );
    currentVideoElement.addEventListener("emptied", handleVideoEmptied); // Important for src changes
    currentVideoElement.addEventListener("abort", handleVideoAbort);

    isVideoStalled = false;
    lastStallTimestamp = 0;
    // Start at minRate, but allow page to settle if it has its own rate.
    // Let ratechange handler sync if page sets a different initial rate.
    currentTargetPlaybackRate = currentVideoElement.playbackRate; // Sync with current before trying to set
    currentTargetPlaybackRate = Math.max(
      AUTO_SPEED_CONFIG.minRate,
      Math.min(currentTargetPlaybackRate, AUTO_SPEED_CONFIG.maxRate),
    );

    setTimeout(() => {
      // Slight delay to ensure player might have set its own rate
      if (currentVideoElement === videoEl) {
        // Check if still the same video
        setVideoPlaybackRate(currentTargetPlaybackRate, "initial_setup");
      }
    }, 300);

    proactiveSpeedIncreaseTimer = setInterval(
      proactiveIncreaseVideoSpeed,
      AUTO_SPEED_CONFIG.proactiveIncreaseIntervalMs,
    );
    bufferMonitorTimer = setInterval(
      monitorBufferAndAdjust,
      AUTO_SPEED_CONFIG.bufferMonitorIntervalMs,
    );

    videoFinderState.isLogicActive = true;
    logAutoSpeed(
      "info",
      `Auto-speed logic activated. ProactiveIncrease: ${AUTO_SPEED_CONFIG.proactiveIncreaseIntervalMs}ms, BufferMonitor: ${AUTO_SPEED_CONFIG.bufferMonitorIntervalMs}ms.`,
    );
  }

  function evaluateAndManageVideoElement() {
    if (!AUTO_SPEED_CONFIG.enabled) {
      if (videoFinderState.isLogicActive)
        stopAllAutoSpeedOperations("auto_speed_disabled");
      return;
    }

    const bestVideo = selectBestVideoElement();

    if (bestVideo) {
      const currentSrc = bestVideo.currentSrc || bestVideo.src;
      if (
        videoFinderState.isLogicActive &&
        currentVideoElement === bestVideo &&
        videoFinderState.lastKnownVideoSrc === currentSrc
      ) {
        // logAutoSpeed('debug', "Still managing the same active video. No change.");
        if (!document.body.contains(currentVideoElement)) {
          // Health check
          logAutoSpeed(
            "warn",
            "Current video element no longer in DOM! Stopping and re-evaluating.",
          );
          stopAllAutoSpeedOperations("video_removed_from_dom");
          // Schedule next evaluation soon because mutation observer might be delayed or miss it
          clearTimeout(videoFinderState.initialFindTimeout);
          videoFinderState.initialFindTimeout = setTimeout(
            evaluateAndManageVideoElement,
            500,
          );
        }
        return; // No change needed
      }

      // New video selected, or current video changed src, or logic wasn't active
      logAutoSpeed(
        "info",
        `New best video selected or change detected. Old: ${videoFinderState.lastKnownVideoSrc}, New: ${currentSrc}.`,
      );
      if (bestVideo.readyState >= 1) {
        // HAVE_METADATA - basic check
        initializeAutoSpeedLogicForVideo(bestVideo);
      } else {
        logAutoSpeed(
          "info",
          `Selected video (${currentSrc}) not ready (readyState: ${bestVideo.readyState}). Waiting for 'loadedmetadata' or 'canplay'.`,
        );
        const onReadyOrError = (event) => {
          bestVideo.removeEventListener("loadedmetadata", onReadyOrError);
          bestVideo.removeEventListener("canplay", onReadyOrError);
          bestVideo.removeEventListener("error", onReadyOrError);
          if (event.type === "error") {
            logAutoSpeed(
              "error",
              `Error loading metadata for ${currentSrc}. Will re-evaluate.`,
            );
            clearTimeout(videoFinderState.initialFindTimeout);
            videoFinderState.initialFindTimeout = setTimeout(
              evaluateAndManageVideoElement,
              AUTO_SPEED_CONFIG.videoFindRetryIntervalMs,
            );
          } else if (document.body.contains(bestVideo)) {
            // Ensure video still exists
            logAutoSpeed(
              "info",
              `Video '${currentSrc}' event '${event.type}' triggered. Proceeding with initialization.`,
            );
            initializeAutoSpeedLogicForVideo(bestVideo);
          } else {
            logAutoSpeed(
              "warn",
              `Video '${currentSrc}' was ready but removed from DOM before init. Re-evaluating.`,
            );
            evaluateAndManageVideoElement();
          }
        };
        bestVideo.addEventListener("loadedmetadata", onReadyOrError, {
          once: true,
        });
        bestVideo.addEventListener("canplay", onReadyOrError, { once: true });
        bestVideo.addEventListener("error", onReadyOrError, { once: true });
      }
    } else {
      // No suitable video found
      if (videoFinderState.isLogicActive) {
        logAutoSpeed(
          "warn",
          "No suitable video found, but logic was active. Stopping for previous video.",
        );
        stopAllAutoSpeedOperations("no_suitable_video_found");
      } else {
        // logAutoSpeed('info', "No suitable video found on this evaluation cycle.");
      }
      // Schedule a retry if the observer doesn't pick it up
      clearTimeout(videoFinderState.initialFindTimeout);
      videoFinderState.initialFindTimeout = setTimeout(
        evaluateAndManageVideoElement,
        AUTO_SPEED_CONFIG.videoFindRetryIntervalMs,
      );
    }
  }

  function startPersistentVideoMonitoring() {
    if (!AUTO_SPEED_CONFIG.enabled) {
      logAutoSpeed(
        "info",
        "Auto-speed disabled by config. Not starting persistent monitoring.",
      );
      return;
    }
    if (videoFinderState.observer) {
      logAutoSpeed("debug", "MutationObserver already started.");
      return; // Already started
    }

    // Initial check
    evaluateAndManageVideoElement();

    const observerCallback = (mutationsList, observer) => {
      let relevantChangeDetected = false;
      for (const mutation of mutationsList) {
        if (mutation.type === "childList") {
          mutation.addedNodes.forEach((node) => {
            if (
              node.nodeType === 1 &&
              (node.tagName === "VIDEO" || node.querySelector("video"))
            )
              relevantChangeDetected = true;
          });
          mutation.removedNodes.forEach((node) => {
            if (
              node.nodeType === 1 &&
              (node.tagName === "VIDEO" || node.contains(currentVideoElement))
            )
              relevantChangeDetected = true;
          });
        } else if (
          mutation.type === "attributes" &&
          mutation.target.tagName === "VIDEO" &&
          (mutation.attributeName === "src" ||
            mutation.attributeName === "currentsrc")
        ) {
          relevantChangeDetected = true;
        }
        // If a video's style/class changes, it might become the new "best" video
        if (
          mutation.type === "attributes" &&
          mutation.target.tagName === "VIDEO" &&
          (mutation.attributeName === "style" ||
            mutation.attributeName === "class")
        ) {
          relevantChangeDetected = true;
        }
      }

      if (relevantChangeDetected) {
        logAutoSpeed(
          "debug",
          "MutationObserver detected relevant DOM change. Debouncing re-evaluation.",
        );
        clearTimeout(videoFinderState.observerDebounceTimeout);
        videoFinderState.observerDebounceTimeout = setTimeout(() => {
          logAutoSpeed(
            "info",
            "Re-evaluating video elements due to DOM changes.",
          );
          evaluateAndManageVideoElement();
        }, AUTO_SPEED_CONFIG.mutationObserverDebounceMs);
      }
    };

    videoFinderState.observer = new MutationObserver(observerCallback);
    videoFinderState.observer.observe(
      document.documentElement || document.body,
      {
        // Observe documentElement for broader coverage including head changes iframes are added etc.
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["src", "currentsrc", "style", "class", "id"], // Monitor these attributes on all elements (subtree)
      },
    );
    logAutoSpeed(
      "info",
      "Persistent video monitoring (MutationObserver) started.",
    );
  }

  // --- END: Persistent Smart Auto-Speed Feature ---

  // --- Original Downloader Logic (largely unchanged) ---
  const _endOfStream = window.MediaSource.prototype.endOfStream;
  window.MediaSource.prototype.endOfStream = function () {
    window.isComplete = 1;
    logAutoSpeed(
      "info",
      "MediaSource endOfStream triggered. Download should be available if autoDownload is on.",
    );
    return _endOfStream.apply(this, arguments);
  };
  // ... (rest of the MediaSource hooks are fine) ...
  window.MediaSource.prototype.endOfStream.toString = function () {
    console.log("[Downloader] endOfStream hook is detecting!");
    return _endOfStream.toString();
  };

  const _addSourceBuffer = window.MediaSource.prototype.addSourceBuffer;
  window.MediaSource.prototype.addSourceBuffer = function (mime) {
    console.log("[Downloader] MediaSource.addSourceBuffer ", mime);
    if (mime.toString().indexOf("audio") !== -1) {
      window.audio = [];
      console.log("[Downloader] audio array cleared.");
    } else if (mime.toString().indexOf("video") !== -1) {
      window.video = [];
      console.log("[Downloader] video array cleared.");
    }
    let sourceBuffer = _addSourceBuffer.call(this, mime);
    const _append = sourceBuffer.appendBuffer;
    sourceBuffer.appendBuffer = function (buffer) {
      if (mime.toString().indexOf("audio") !== -1) {
        window.audio.push(buffer);
      } else if (mime.toString().indexOf("video") !== -1) {
        window.video.push(buffer);
      }
      _append.call(this, buffer);
    };
    sourceBuffer.appendBuffer.toString = function () {
      console.log("[Downloader] appendSourceBuffer hook is detecting!");
      return _append.toString();
    };
    return sourceBuffer;
  };
  window.MediaSource.prototype.addSourceBuffer.toString = function () {
    console.log("[Downloader] addSourceBuffer hook is detecting!");
    return _addSourceBuffer.toString();
  };

  function downloadFiles() {
    if (window.audio.length === 0 && window.video.length === 0) {
      console.log("[Downloader] No audio or video data captured to download.");
      return;
    }
    const title = (document.title || "media")
      .replace(/[<>:"/\\|?*]+/g, "_")
      .substring(0, 100);
    if (window.audio.length > 0) {
      try {
        let audioBlob = new Blob(window.audio);
        let a = document.createElement("a");
        a.href = window.URL.createObjectURL(audioBlob);
        a.download = `audio_${title}.mp4`;
        a.click();
        window.URL.revokeObjectURL(a.href);
        console.log("[Downloader] Audio download triggered.");
      } catch (e) {
        console.error("[Downloader] Error triggering audio download:", e);
      }
    }
    if (window.video.length > 0) {
      try {
        let videoBlob = new Blob(window.video);
        let a = document.createElement("a");
        a.href = window.URL.createObjectURL(videoBlob);
        a.download = `video_${title}.mp4`;
        a.click();
        window.URL.revokeObjectURL(a.href);
        console.log("[Downloader] Video download triggered.");
      } catch (e) {
        console.error("[Downloader] Error triggering video download:", e);
      }
    }
    window.downloadAll = 0;
    window.isComplete = 0;
  }

  setInterval(() => {
    if (window.downloadAll === 1) {
      console.log("[Downloader] Manual download trigger detected.");
      downloadFiles();
    }
  }, 2000);
  if (window.autoDownload === 1) {
    setInterval(() => {
      if (window.isComplete === 1) {
        console.log("[Downloader] Auto-download condition met.");
        downloadFiles();
      }
    }, 2000);
  }

  // Iframe sandbox removal
  (function (that) {
    setInterval(() => {
      const iframes = that.document.querySelectorAll("iframe[sandbox]");
      if (iframes.length > 0) {
        iframes.forEach((ifr) => {
          ifr.removeAttribute("sandbox");
        });
        logAutoSpeed(
          "info",
          `Processed ${iframes.length} sandboxed iframes (removed sandbox attribute).`,
        );
      }
    }, 5000); // Check periodically
  })(window);

  // --- Global Initialization ---
  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    setTimeout(
      startPersistentVideoMonitoring,
      AUTO_SPEED_CONFIG.initialGlobalDelayMs,
    );
  } else {
    window.addEventListener(
      "DOMContentLoaded",
      () => {
        setTimeout(
          startPersistentVideoMonitoring,
          AUTO_SPEED_CONFIG.initialGlobalDelayMs,
        );
      },
      { once: true },
    );
  }
})();
