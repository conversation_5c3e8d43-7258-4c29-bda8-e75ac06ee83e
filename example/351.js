// ==UserScript==
// @name         Aria Downloader 媒体捕获工具 (智能混合版)
// @namespace    aria-downloader.io
// @version      3.5.1
// @description  智能媒体捕获工具，融合v3.3.0礼貌激活机制与v3.4.3多标签页修复，实现最佳的智能化捕获体验
// <AUTHOR> Project (Production Build)
// @match        *://*.youtube.com/*
// @match        *://*.youtube-nocookie.com/*
// @match        *://*.bilibili.com/*
// @match        *://*.vimeo.com/*
// @match        *://*.netflix.com/*
// @match        *://*.twitch.tv/*
// @match        *://*.hulu.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_deleteValue
// @grant        GM_listValues
// @run-at       document-start
// ==/UserScript==

console.log("[Aria Bootstrapper] Script injected successfully. Version 3.5.1");

// 用户可配置参数
const ARIA_USER_CONFIG = {
  SPEED_HIGH_WATERMARK: 45,
  SPEED_LOW_WATERMARK: 15,
  SPEED_SMOOTHING_FACTOR: 0.1,
  DEFAULT_MAX_SPEED: 8.0,
  HEARTBEAT_INTERVAL: 5000,
  HEARTBEAT_TIMEOUT: 60000, // v3.4.2 修复：增加到60秒，避免多标签页误杀
  STALL_CHECK_INTERVAL: 2000,
  STALL_THRESHOLD: 5,
  MAX_CAPTURE_SIZE_MB: 4096, // 最大捕获大小 4GB
  CHUNK_BATCH_SIZE: 50, // 批处理大小
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  CLEANUP_INTERVAL: 600000, // v3.4.2 修复：增加到10分钟清理一次，减少冲突
  SESSION_EXPIRE_TIME: 1800000, // v3.4.2 修复：增加到30分钟后过期
  BLOB_URL_REVOKE_DELAY: 15000, // 15秒后释放Blob URL
  VIDEO_ELEMENT_TIMEOUT: 20000, // 20秒视频元素检测超时
  ACTIVATION_DELAY: 750, // 礼貌激活延迟（毫秒）
};

(function () {
  "use strict";

  // v3.2.0: 添加iframe检测，确保只在顶层页面执行
  if (window.self !== window.top) {
    console.log("[Aria] Script blocked in iframe");
    return;
  }

  const AriaDownloader = (() => {
    // 日志管理器
    class Logger {
      constructor() {
        this.level = "info";
      }

      setLevel(level) {
        this.level = level;
        this.info(`Log level set to: ${level}`);
      }

      log(level, ...args) {
        const levels = { none: 0, error: 1, info: 2, debug: 3 };
        if (levels[level] <= levels[this.level]) {
          console.log(`[Aria ${level.toUpperCase()}]`, ...args);
        }
      }

      error(...args) {
        this.log("error", ...args);
      }
      info(...args) {
        this.log("info", ...args);
      }
      debug(...args) {
        this.log("debug", ...args);
      }
    }

    // 心跳管理器 - 无状态版本
    class HeartbeatManager {
      constructor() {
        this.heartbeatKey = "aria_active_sessions";
      }

      // 直接操作localStorage，不维护内部状态
      updateSession(sessionId, sessionData) {
        try {
          const heartbeats = this.getAllStoredSessions();
          heartbeats[sessionId] = {
            timestamp: Date.now(),
            url: window.location.href,
            ...sessionData,
          };
          localStorage.setItem(this.heartbeatKey, JSON.stringify(heartbeats));
        } catch (e) {
          AriaController.logger.error("Failed to update heartbeat:", e);
          if (e.name === "QuotaExceededError") {
            AriaController.getInstance().requestCleanup();
          }
        }
      }

      removeSession(sessionId) {
        try {
          const heartbeats = this.getAllStoredSessions();
          delete heartbeats[sessionId];
          localStorage.setItem(this.heartbeatKey, JSON.stringify(heartbeats));
        } catch (e) {
          AriaController.logger.error("Failed to remove heartbeat:", e);
        }
      }

      // 返回所有活跃会话（时间戳未过期的）
      getActiveSessions() {
        const now = Date.now();
        const allSessions = this.getAllStoredSessions();
        const active = {};

        for (const [id, data] of Object.entries(allSessions)) {
          if (now - data.timestamp < ARIA_USER_CONFIG.HEARTBEAT_TIMEOUT) {
            active[id] = data;
          }
        }

        return active;
      }

      // 获取所有存储的会话（包括过期的）
      getAllStoredSessions() {
        try {
          return JSON.parse(localStorage.getItem(this.heartbeatKey) || "{}");
        } catch (e) {
          AriaController.logger.error("Failed to parse heartbeats:", e);
          return {};
        }
      }

      // 清理过期的会话数据
      cleanupExpiredSessions() {
        const now = Date.now();
        const allSessions = this.getAllStoredSessions();
        const cleaned = {};
        let hasChanges = false;

        for (const [id, data] of Object.entries(allSessions)) {
          if (now - data.timestamp < ARIA_USER_CONFIG.HEARTBEAT_TIMEOUT * 2) {
            cleaned[id] = data;
          } else {
            hasChanges = true;
          }
        }

        if (hasChanges) {
          try {
            localStorage.setItem(this.heartbeatKey, JSON.stringify(cleaned));
          } catch (e) {
            AriaController.logger.error("Failed to cleanup heartbeats:", e);
          }
        }

        return hasChanges;
      }
    }

    // 数据库管理器
    class DBManager {
      constructor() {
        this.dbName = "AriaDownloaderDB_v3";
        this.version = 1;
        this.db = null;
      }

      async init() {
        try {
          await this.openDatabase();
        } catch (error) {
          if (
            error.name === "VersionError" ||
            error.name === "InvalidStateError"
          ) {
            AriaController.logger.error(
              "DB error detected. Attempting recovery...",
            );
            if (this.db) this.db.close();
            await this.destroyDatabase();
            AriaController.logger.info(
              "Corrupted DB deleted. Retrying initialization.",
            );
            await this.openDatabase();
          } else {
            throw error;
          }
        }
      }

      async destroyDatabase() {
        return new Promise((resolve, reject) => {
          const req = indexedDB.deleteDatabase(this.dbName);
          req.onsuccess = resolve;
          req.onerror = reject;
          req.onblocked = () => {
            AriaController.logger.warn("DB deletion blocked, waiting...");
            setTimeout(resolve, 1000);
          };
        });
      }

      async openDatabase() {
        return new Promise((resolve, reject) => {
          const request = indexedDB.open(this.dbName, this.version);

          request.onerror = (e) => {
            AriaController.logger.error(
              "Failed to open database:",
              e.target.error,
            );
            reject(e.target.error);
          };

          request.onsuccess = (e) => {
            this.db = e.target.result;

            this.db.onerror = (event) => {
              AriaController.logger.error(
                "Database error:",
                event.target.error,
              );
            };

            resolve();
          };

          request.onupgradeneeded = (e) => {
            const db = e.target.result;

            if (db.objectStoreNames.contains("chunks")) {
              db.deleteObjectStore("chunks");
            }

            const store = db.createObjectStore("chunks", {
              keyPath: "id",
              autoIncrement: true,
            });
            store.createIndex("sessionId", "sessionId", { unique: false });
            store.createIndex("type", "type", { unique: false });
            store.createIndex("timestamp", "timestamp", { unique: false });
          };
        });
      }

      // 只返回所有会话ID，不做任何清理决策
      async getAllSessionIds() {
        if (!this.db) return [];

        const transaction = this.db.transaction(["chunks"], "readonly");
        const store = transaction.objectStore("chunks");
        const index = store.index("sessionId");

        return new Promise((resolve, reject) => {
          const request = index.getAllKeys();
          request.onsuccess = () => {
            const uniqueIds = [...new Set(request.result)];
            resolve(uniqueIds);
          };
          request.onerror = (e) => reject(e.target.error);
        });
      }

      async deleteSession(sessionId) {
        if (!this.db) return;

        const transaction = this.db.transaction(["chunks"], "readwrite");
        const store = transaction.objectStore("chunks");
        const index = store.index("sessionId");

        return new Promise((resolve, reject) => {
          const request = index.openCursor(IDBKeyRange.only(sessionId));

          request.onsuccess = (e) => {
            const cursor = e.target.result;
            if (cursor) {
              cursor.delete();
              cursor.continue();
            } else {
              resolve();
            }
          };

          request.onerror = reject;
        });
      }

      async saveChunk(sessionId, type, data) {
        if (!this.db) throw new Error("Database not initialized");

        const transaction = this.db.transaction(["chunks"], "readwrite");

        return new Promise((resolve, reject) => {
          const request = transaction.objectStore("chunks").add({
            sessionId,
            type,
            data,
            timestamp: Date.now(),
          });

          request.onsuccess = resolve;
          request.onerror = (e) => {
            if (e.target.error.name === "QuotaExceededError") {
              AriaController.logger.error("Storage quota exceeded");
              reject(new Error("QUOTA_EXCEEDED"));
            } else {
              reject(e.target.error);
            }
          };
        });
      }

      async getChunks(sessionId, type) {
        if (!this.db) return [];

        const transaction = this.db.transaction(["chunks"], "readonly");
        const index = transaction.objectStore("chunks").index("sessionId");

        return new Promise((resolve, reject) => {
          const request = index.getAll(sessionId);

          request.onsuccess = () => {
            const chunks = request.result;
            if (type) {
              resolve(
                chunks
                  .filter((c) => c.type === type)
                  .sort((a, b) => a.id - b.id),
              );
            } else {
              resolve(chunks.sort((a, b) => a.id - b.id));
            }
          };

          request.onerror = (e) => reject(e.target.error);
        });
      }

      async checkStorageUsage() {
        if ("storage" in navigator && "estimate" in navigator.storage) {
          try {
            const estimate = await navigator.storage.estimate();
            const usagePercent = (estimate.usage / estimate.quota) * 100;
            return {
              usage: estimate.usage,
              quota: estimate.quota,
              percent: usagePercent,
            };
          } catch (e) {
            AriaController.logger.error("Failed to check storage usage:", e);
          }
        }
        return null;
      }
    }

    // 增强的速度控制器（整合了停滞检测）
    class AdaptiveSpeedController {
      constructor(session) {
        this.session = session;
        this.videoElement = session.videoElement;
        this.animationFrameId = null;
        this.lastBufferCheck = 0;
        this.config = {
          HIGH_WATERMARK: ARIA_USER_CONFIG.SPEED_HIGH_WATERMARK,
          LOW_WATERMARK: ARIA_USER_CONFIG.SPEED_LOW_WATERMARK,
          SMOOTHING: ARIA_USER_CONFIG.SPEED_SMOOTHING_FACTOR,
          MAX_SPEED: ARIA_USER_CONFIG.DEFAULT_MAX_SPEED,
          NORMAL_SPEED: 1.0,
        };

        // 停滞检测相关
        this.stallCount = 0;
        this.lastCurrentTime = 0;
        this.lastBufferedEnd = 0;
        this.lastStallCheck = 0;
      }

      updateMaxSpeed(speed) {
        this.config.MAX_SPEED =
          parseFloat(speed) || ARIA_USER_CONFIG.DEFAULT_MAX_SPEED;
        AriaController.logger.info(
          `Max speed updated to: ${this.config.MAX_SPEED}x`,
        );
      }

      start() {
        if (!this.videoElement || this.animationFrameId) return;

        this.updateMaxSpeed(AriaDownloader.getInstance().settings.maxSpeed);
        AriaController.logger.info(
          `Adaptive speed control started for session ${this.session.id}`,
        );
        this.monitor();
      }

      stop() {
        if (this.animationFrameId) {
          cancelAnimationFrame(this.animationFrameId);
          this.animationFrameId = null;

          if (this.videoElement && !this.videoElement.paused) {
            try {
              this.videoElement.playbackRate = this.config.NORMAL_SPEED;
            } catch (e) {
              AriaController.logger.debug("Failed to reset playback rate:", e);
            }
          }

          AriaController.logger.info(
            `Adaptive speed control stopped for session ${this.session.id}`,
          );
        }
      }

      monitor = () => {
        if (
          !this.videoElement ||
          this.videoElement.paused ||
          this.session.state === "complete"
        ) {
          this.stop();
          return;
        }

        const now = performance.now();

        // 速度调整逻辑（100ms间隔）
        if (now - this.lastBufferCheck > 100) {
          this.lastBufferCheck = now;

          try {
            if (this.videoElement.buffered.length > 0) {
              const bufferEnd = this.videoElement.buffered.end(
                this.videoElement.buffered.length - 1,
              );
              const bufferAhead = bufferEnd - this.videoElement.currentTime;
              let targetRate;

              if (bufferAhead <= this.config.LOW_WATERMARK) {
                targetRate = this.config.NORMAL_SPEED;
              } else if (bufferAhead >= this.config.HIGH_WATERMARK) {
                targetRate = this.config.MAX_SPEED;
              } else {
                const bufferRange =
                  this.config.HIGH_WATERMARK - this.config.LOW_WATERMARK;
                const speedRange =
                  this.config.MAX_SPEED - this.config.NORMAL_SPEED;
                const bufferProgress =
                  (bufferAhead - this.config.LOW_WATERMARK) / bufferRange;
                targetRate =
                  this.config.NORMAL_SPEED + speedRange * bufferProgress;
              }

              const currentRate = this.videoElement.playbackRate;
              const newRate =
                currentRate +
                (targetRate - currentRate) * this.config.SMOOTHING;

              if (Math.abs(newRate - currentRate) > 0.01) {
                this.videoElement.playbackRate = newRate;
              }

              AriaController.logger.debug(
                `Buffer: ${bufferAhead.toFixed(1)}s, Speed: ${newRate.toFixed(2)}x`,
              );
            }
          } catch (e) {
            AriaController.logger.debug("Speed control error:", e);
          }
        }

        // 停滞检测逻辑（每2秒检查一次）
        if (now - this.lastStallCheck > ARIA_USER_CONFIG.STALL_CHECK_INTERVAL) {
          this.lastStallCheck = now;
          this.checkForStall();
        }

        this.animationFrameId = requestAnimationFrame(this.monitor);
      };

      checkForStall() {
        if (
          !this.videoElement ||
          this.session.state !== "capturing" ||
          this.videoElement.paused
        ) {
          return;
        }

        try {
          const currentTime = this.videoElement.currentTime;

          // 检查播放是否停滞
          if (Math.abs(currentTime - this.lastCurrentTime) < 0.1) {
            if (
              !this.videoElement.paused &&
              this.videoElement.readyState >= 2
            ) {
              if (this.videoElement.buffered.length > 0) {
                const bufferedEnd = this.videoElement.buffered.end(
                  this.videoElement.buffered.length - 1,
                );

                if (bufferedEnd > currentTime + 1) {
                  this.stallCount++;

                  if (this.stallCount >= ARIA_USER_CONFIG.STALL_THRESHOLD) {
                    this.handleStall();
                  }
                } else if (bufferedEnd === this.lastBufferedEnd) {
                  this.stallCount++;

                  if (this.stallCount >= ARIA_USER_CONFIG.STALL_THRESHOLD) {
                    this.handleStall();
                  }
                }
              }
            }
          } else {
            this.stallCount = 0;
          }

          this.lastCurrentTime = currentTime;
          if (this.videoElement.buffered.length > 0) {
            this.lastBufferedEnd = this.videoElement.buffered.end(
              this.videoElement.buffered.length - 1,
            );
          }
        } catch (e) {
          AriaController.logger.error("Stall detection error:", e);
        }
      }

      handleStall() {
        AriaController.logger.info(
          `Stall detected for session ${this.session.id}`,
        );
        this.session.markAsStalled();
        AriaController.getInstance().guiManager.showStalledState(
          this.session.id,
        );
        this.stop();
      }
    }

    // 捕获会话 - 优化版
    class CaptureSession {
      constructor(id, mediaSource, isResume = false) {
        this.id = id;
        this.mediaSource = mediaSource;
        this.state = "capturing";
        this.videoElement = null;
        this.capturedSize = { video: 0, audio: 0 };
        this.startTime = Date.now();
        this.dbManager = AriaDownloader.getInstance().dbManager;
        this.speedController = null;
        this.preparedBlobs = { video: null, audio: null };
        this.isResume = isResume;
        this.eventListeners = new Map();
        this.chunkQueue = { video: [], audio: [] };
        this.processingQueue = false;

        // 存储文件扩展名
        this.fileExtensions = { video: ".webm", audio: ".webm" };

        // 存储Blob URL以便后续释放
        this.blobUrlTimers = new Map();

        // 如果是恢复会话，加载之前的状态
        if (isResume) {
          this.loadResumeState();
        }
      }

      loadResumeState() {
        try {
          const controller = AriaDownloader.getInstance();
          const activeSessions =
            controller.heartbeatManager.getActiveSessions();
          const sessionData = activeSessions[this.id];

          if (sessionData) {
            if (sessionData.capturedSize) {
              this.capturedSize = sessionData.capturedSize;
            }
            if (sessionData.startTime) {
              this.startTime = sessionData.startTime;
            }
            if (sessionData.fileExtensions) {
              this.fileExtensions = sessionData.fileExtensions;
            }
            AriaController.logger.info(
              `Resumed session ${this.id} with previous state`,
            );
          }
        } catch (e) {
          AriaController.logger.error("Failed to load resume state:", e);
        }
      }

      setMimeType(type, mimeType) {
        // 解析mime类型以确定文件扩展名
        const mimeToExt = {
          "video/webm": ".webm",
          "video/mp4": ".mp4",
          "video/x-matroska": ".mkv",
          "audio/webm": ".webm",
          "audio/mp4": ".m4a",
          "audio/mpeg": ".mp3",
          "audio/ogg": ".ogg",
        };

        const ext = mimeToExt[mimeType] || ".webm";
        this.fileExtensions[type] = ext;

        AriaController.logger.debug(
          `Set ${type} extension to ${ext} based on mime type: ${mimeType}`,
        );
      }

      getHeartbeatData() {
        return {
          state: this.state,
          capturedSize: this.capturedSize,
          startTime: this.startTime,
          currentTime: this.videoElement?.currentTime || 0,
          fileExtensions: this.fileExtensions,
        };
      }

      async captureData(type, buffer) {
        // 检查存储限制
        const totalSizeMB =
          (this.capturedSize.video + this.capturedSize.audio) / 1024 / 1024;
        if (totalSizeMB >= ARIA_USER_CONFIG.MAX_CAPTURE_SIZE_MB) {
          AriaController.logger.warn(
            `Capture size limit reached: ${totalSizeMB}MB`,
          );
          this.complete();
          return;
        }

        // 添加到队列
        this.chunkQueue[type].push(buffer.slice(0));

        // 批处理队列
        if (!this.processingQueue) {
          this.processChunkQueue();
        }
      }

      async processChunkQueue() {
        this.processingQueue = true;

        try {
          for (const type of ["video", "audio"]) {
            while (this.chunkQueue[type].length > 0) {
              const batch = this.chunkQueue[type].splice(
                0,
                ARIA_USER_CONFIG.CHUNK_BATCH_SIZE,
              );

              for (const chunk of batch) {
                let retries = 0;
                let saved = false;

                while (retries < ARIA_USER_CONFIG.RETRY_ATTEMPTS && !saved) {
                  try {
                    await this.dbManager.saveChunk(this.id, type, chunk);
                    this.capturedSize[type] += chunk.byteLength;
                    saved = true;
                  } catch (e) {
                    retries++;

                    if (e.message === "QUOTA_EXCEEDED") {
                      AriaController.logger.error(
                        "Storage quota exceeded, stopping capture",
                      );
                      this.complete();
                      return;
                    }

                    if (retries < ARIA_USER_CONFIG.RETRY_ATTEMPTS) {
                      AriaController.logger.debug(
                        `Retry ${retries} for chunk save`,
                      );
                      await new Promise((resolve) =>
                        setTimeout(
                          resolve,
                          ARIA_USER_CONFIG.RETRY_DELAY * retries,
                        ),
                      );
                    } else {
                      throw e;
                    }
                  }
                }
              }

              // 通知控制器更新心跳
              AriaDownloader.getInstance().updateSessionHeartbeat(
                this.id,
                this.getHeartbeatData(),
              );
            }
          }
        } catch (e) {
          this.state = "error";
          AriaController.logger.error(
            `Failed to save chunks for session ${this.id}:`,
            e,
          );
        } finally {
          this.processingQueue = false;

          // 如果还有剩余数据，继续处理
          if (
            this.chunkQueue.video.length > 0 ||
            this.chunkQueue.audio.length > 0
          ) {
            setTimeout(() => this.processChunkQueue(), 100);
          }
        }
      }

      markAsStalled() {
        this.state = "stalled";

        const stallData = {
          ...this.getHeartbeatData(),
          stallTime: this.videoElement?.currentTime || 0,
          state: "stalled",
        };

        // 通知控制器更新心跳
        AriaDownloader.getInstance().updateSessionHeartbeat(this.id, stallData);

        if (this.speedController) {
          this.speedController.stop();
        }
      }

      async complete() {
        if (this.state === "complete") return;

        this.state = "complete";

        if (this.speedController) {
          this.speedController.stop();
        }

        // 等待所有队列处理完成
        while (
          this.processingQueue ||
          this.chunkQueue.video.length > 0 ||
          this.chunkQueue.audio.length > 0
        ) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }

        // 预准备Blob URLs
        await this.prepareBlobUrls();

        // 通知控制器
        AriaDownloader.getInstance().onSessionComplete(this);
      }

      async prepareBlobUrls() {
        try {
          AriaController.logger.info(
            `Preparing blob URLs for session ${this.id}`,
          );

          // 视频Blob
          const videoChunks = await this.dbManager.getChunks(this.id, "video");
          if (videoChunks.length > 0) {
            const videoData = videoChunks.map((c) => c.data);
            const videoBlob = new Blob(videoData, { type: "video/webm" });
            this.preparedBlobs.video = URL.createObjectURL(videoBlob);
            AriaController.logger.debug(
              `Video blob prepared: ${videoData.length} chunks`,
            );
          }

          // 音频Blob
          const audioChunks = await this.dbManager.getChunks(this.id, "audio");
          if (audioChunks.length > 0) {
            const audioData = audioChunks.map((c) => c.data);
            const audioBlob = new Blob(audioData, { type: "audio/webm" });
            this.preparedBlobs.audio = URL.createObjectURL(audioBlob);
            AriaController.logger.debug(
              `Audio blob prepared: ${audioData.length} chunks`,
            );
          }

          AriaController.logger.info(
            `Blob URLs prepared for session ${this.id}`,
          );
        } catch (e) {
          AriaController.logger.error(
            `Failed to prepare blob URLs for session ${this.id}:`,
            e,
          );
        }
      }

      // 计划释放Blob URL
      scheduleBlobUrlRevoke(type, url) {
        if (this.blobUrlTimers.has(type)) {
          clearTimeout(this.blobUrlTimers.get(type));
        }

        const timer = setTimeout(() => {
          URL.revokeObjectURL(url);
          this.blobUrlTimers.delete(type);
          AriaController.logger.debug(`Blob URL revoked for ${type}`);
        }, ARIA_USER_CONFIG.BLOB_URL_REVOKE_DELAY);

        this.blobUrlTimers.set(type, timer);
      }

      cleanup() {
        AriaController.logger.info(`Cleaning up session ${this.id}`);

        if (this.speedController) {
          this.speedController.stop();
        }

        // 清除所有定时器
        for (const timer of this.blobUrlTimers.values()) {
          clearTimeout(timer);
        }
        this.blobUrlTimers.clear();

        // 释放Blob URLs
        if (this.preparedBlobs.video) {
          URL.revokeObjectURL(this.preparedBlobs.video);
          this.preparedBlobs.video = null;
        }

        if (this.preparedBlobs.audio) {
          URL.revokeObjectURL(this.preparedBlobs.audio);
          this.preparedBlobs.audio = null;
        }

        // 移除所有事件监听器
        for (const [element, listeners] of this.eventListeners) {
          for (const [event, handler] of listeners) {
            element.removeEventListener(event, handler);
          }
        }
        this.eventListeners.clear();

        // 清空队列
        this.chunkQueue = { video: [], audio: [] };
      }

      addEventListener(element, event, handler) {
        if (!this.eventListeners.has(element)) {
          this.eventListeners.set(element, new Map());
        }
        this.eventListeners.get(element).set(event, handler);
        element.addEventListener(event, handler);
      }
    }

    // 劫持管理器 - v3.2.0 重构版
    class HijackManager {
      constructor() {
        this.originalAddSourceBuffer = MediaSource.prototype.addSourceBuffer;
        this.originalEndOfStream = MediaSource.prototype.endOfStream;
        this.mediaSourceMap = new WeakMap();
        this.hijacked = false;
      }

      init() {
        if (this.hijacked) return;

        const self = this;

        try {
          // v3.4.3 增强：添加详细的劫持日志
          AriaController.logger.info("Starting MediaSource hijack...");

          MediaSource.prototype.addSourceBuffer = function (mime) {
            AriaController.logger.info(
              `MediaSource.addSourceBuffer called with mime: ${mime}`,
            );
            AriaController.logger.debug(`MediaSource instance:`, this);
            AriaController.logger.debug(
              `MediaSource readyState: ${this.readyState}`,
            );

            let session = self.mediaSourceMap.get(this);

            if (!session) {
              // 检查是否是恢复会话
              const urlParams = new URLSearchParams(window.location.search);
              const resumeId = urlParams.get("aria_resume_id");
              const sessionId =
                resumeId ||
                `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

              session = new CaptureSession(sessionId, this, !!resumeId);
              self.mediaSourceMap.set(this, session);

              // 使用setTimeout避免同步调用栈问题
              setTimeout(() => {
                AriaDownloader.getInstance().registerSession(session);
              }, 0);
            }

            const sourceBuffer = self.originalAddSourceBuffer.call(this, mime);
            const originalAppend = sourceBuffer.appendBuffer;
            const type = mime.includes("audio") ? "audio" : "video";

            // 解析并存储mime类型
            session.setMimeType(type, mime);

            sourceBuffer.appendBuffer = function (buffer) {
              if (session.state === "capturing") {
                session.captureData(type, buffer);
              }
              return originalAppend.call(this, buffer);
            };

            // v3.2.0 核心改动：在这里调用 attachSpeedController
            // 确保会话已经有了视频元素后才附加速度控制器
            if (session.videoElement && !session.speedController) {
              AriaDownloader.getInstance().attachSpeedController(session);
            }

            return sourceBuffer;
          };

          MediaSource.prototype.endOfStream = function () {
            const session = self.mediaSourceMap.get(this);
            if (session && session.state === "capturing") {
              session.complete();
            }
            return self.originalEndOfStream.call(this);
          };

          this.hijacked = true;
          AriaController.logger.info("MediaSource hijacked successfully");

          // v3.5.0 增强：更强的 MediaSource 创建监控
          const originalMediaSource = window.MediaSource;
          window.MediaSource = function (...args) {
            const instance = new originalMediaSource(...args);
            AriaController.logger.info("New MediaSource created");
            AriaController.logger.debug(
              `MediaSource instance created:`,
              instance,
            );

            // 监控 MediaSource 状态变化
            const originalAddEventListener = instance.addEventListener;
            instance.addEventListener = function (event, handler, options) {
              if (event === "sourceopen") {
                AriaController.logger.info(
                  "MediaSource sourceopen event listener added",
                );
              }
              return originalAddEventListener.call(
                this,
                event,
                handler,
                options,
              );
            };

            return instance;
          };
          // 保持原型链
          window.MediaSource.prototype = originalMediaSource.prototype;
          Object.setPrototypeOf(window.MediaSource, originalMediaSource);

          // v3.5.0 增强：监控 URL.createObjectURL 调用
          const originalCreateObjectURL = URL.createObjectURL;
          URL.createObjectURL = function (object) {
            const url = originalCreateObjectURL.call(this, object);
            if (object instanceof MediaSource) {
              AriaController.logger.info(
                `MediaSource blob URL created: ${url}`,
              );
            }
            return url;
          };
        } catch (e) {
          AriaController.logger.error("Failed to hijack MediaSource:", e);
        }
      }
    }

    // GUI管理器 - 最终优化版
    class GUIManager {
      constructor(controller) {
        this.controller = controller;
        this.shadowHost = null;
        this.shadowRoot = null;
        this.elements = {};
        this.currentSessionId = null;
        this.updateInterval = null;
        this.initialized = false;
      }

      init() {
        if (this.initialized) return;

        if (document.body) {
          this.createGUI();
        } else {
          const observer = new MutationObserver((mutations, obs) => {
            if (document.body) {
              obs.disconnect();
              this.createGUI();
            }
          });

          observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
          });
        }
      }

      createGUI() {
        if (this.shadowHost) return;

        try {
          this.shadowHost = document.createElement("div");
          this.shadowHost.id = "aria-downloader-host";
          document.body.appendChild(this.shadowHost);

          // 创建Shadow DOM
          this.shadowRoot = this.shadowHost.attachShadow({ mode: "closed" });

          // 注入样式和HTML
          this.shadowRoot.innerHTML = `
                        <style>
                            :host{position:fixed;top:20px;right:20px;z-index:2147483647;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif}
                            .aria-icon{width:48px;height:48px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;opacity:.7;transition:all .3s ease;box-shadow:0 2px 10px rgba(0,0,0,.2)}
                            .aria-icon:hover{opacity:1;transform:scale(1.1)}
                            .aria-icon svg{width:24px;height:24px;fill:#fff}
                            .aria-panel{display:none;position:absolute;top:60px;right:0;width:320px;background-color:#1a1a1a;border-radius:12px;padding:20px;color:#fff;font-size:14px;box-shadow:0 10px 30px rgba(0,0,0,.3)}
                            .aria-panel.show{display:block}
                            .panel-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;padding-bottom:10px;border-bottom:1px solid #333}
                            .panel-title{font-size:18px;font-weight:600}
                            .panel-version{font-size:12px;color:#666}
                            .status-section,.settings-section{background-color:#262626;border-radius:8px;padding:15px;margin-bottom:15px}
                            .status-item{display:flex;justify-content:space-between;margin-bottom:8px;font-size:13px}
                            .status-item:last-child{margin-bottom:0}
                            .status-label{color:#999}
                            .status-value{font-weight:bold;color:#fff}
                            .status-value.capturing{color:#4ade80}
                            .status-value.complete{color:#60a5fa}
                            .status-value.stalled{color:#fbbf24}
                            .status-value.error{color:#ef4444}
                            .action-buttons{display:grid;grid-template-columns:1fr 1fr;gap:10px;margin-bottom:10px}
                            .btn{padding:10px;border:none;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s;color:#fff}
                            .btn-primary{background-color:#4a5568}
                            .btn-primary:not(:disabled):hover{background-color:#2d3748}
                            .btn-validate{grid-column:1 / -1;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%)}
                            .btn-resume{grid-column:1 / -1;background:#ef4444;margin-top:10px}
                            .btn:disabled{opacity:.5;cursor:not-allowed}
                            .toggle-item{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px}
                            .toggle-item:last-child{margin-bottom:0}
                            .toggle-label{font-size:13px;color:#ccc}
                            .toggle-switch{position:relative;width:40px;height:22px;background-color:#374151;border-radius:11px;cursor:pointer;transition:background .2s}
                            .toggle-switch.active{background-color:#667eea}
                            .toggle-switch::after{content:'';position:absolute;top:2px;left:2px;width:18px;height:18px;background-color:#fff;border-radius:50%;transition:transform .2s}
                            .toggle-switch.active::after{transform:translateX(18px)}
                            #session-selector{width:100%;padding:8px;background-color:#374151;border:none;border-radius:4px;color:#fff;margin-bottom:10px;display:none}
                            .setting-input-item{display:flex;justify-content:space-between;align-items:center}
                            .setting-input-item input{width:60px;background-color:#374151;border:1px solid #4a5568;color:#fff;border-radius:4px;padding:4px;text-align:center}
                            .completion-notice{position:absolute;top:0;right:60px;background:linear-gradient(135deg,#10b981 0%,#059669 100%);color:#fff;padding:12px 20px;border-radius:8px;box-shadow:0 4px 15px rgba(0,0,0,.2);font-size:15px;font-weight:500;animation:fadeInOut 4s ease-in-out forwards;pointer-events:none}
                            .stall-notice{background:#fbbf24;color:#1a1a1a;padding:10px;border-radius:6px;margin-bottom:10px;font-size:12px}
                            .storage-warning{background:#ef4444;color:#fff;padding:8px;border-radius:4px;margin-bottom:10px;font-size:12px}
                            @keyframes fadeInOut{0%,100%{opacity:0;transform:translateY(-20px)}10%,90%{opacity:1;transform:translateY(0)}}
                        </style>
                        <div class="aria-icon" id="aria-icon">
                            <svg viewBox="0 0 24 24"><path d="M19,9L20.25,6.25L23,5L20.25,3.75L19,1L17.75,3.75L15,5L17.75,6.25L19,9M19,15L17.75,17.75L15,19L17.75,20.25L19,23L20.25,20.25L23,19L20.25,17.75L19,15M11.5,9.5L9.5,4L7.5,9.5L2,11.5L7.5,13.5L9.5,19L11.5,13.5L17,11.5L11.5,9.5Z"/></svg>
                        </div>
                        <div class="aria-panel" id="aria-panel">
                            <div class="panel-header">
                                <h2 class="panel-title">Aria Downloader</h2>
                                <span class="panel-version">v3.5.1</span>
                            </div>
                            <select id="session-selector"></select>
                            <div id="stall-notice-container"></div>
                            <div id="storage-warning-container"></div>
                            <div class="status-section">
                                <div class="status-item">
                                    <span class="status-label">状态:</span>
                                    <span class="status-value" id="status">等待捕获...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">当前倍速:</span>
                                    <span class="status-value" id="current-speed">1.00x</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">视频大小:</span>
                                    <span class="status-value" id="video-size">0 MB</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">音频大小:</span>
                                    <span class="status-value" id="audio-size">0 MB</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">捕获时长:</span>
                                    <span class="status-value" id="duration">0:00</span>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-primary" id="dl-video" disabled>下载视频</button>
                                <button class="btn btn-primary" id="dl-audio" disabled>下载音频</button>
                            </div>
                            <button class="btn btn-validate" id="dl-validate" disabled>全部下载</button>
                            <div class="settings-section" style="margin-top: 15px;">
                                <div class="toggle-item">
                                    <span class="toggle-label">启用智能无级变速</span>
                                    <div class="toggle-switch" data-setting="enableSpeedControl"></div>
                                </div>
                                <div class="toggle-item setting-input-item">
                                    <span class="toggle-label">最高变速倍率</span>
                                    <input type="number" id="max-speed-input" min="1" max="16" step="0.5">
                                </div>
                                <div class="toggle-item">
                                    <span class="toggle-label">捕获完成后自动下载</span>
                                    <div class="toggle-switch" data-setting="autoDownload"></div>
                                </div>
                                <div class="toggle-item">
                                    <span class="toggle-label">调试模式</span>
                                    <div class="toggle-switch" data-setting="debugMode"></div>
                                </div>
                            </div>
                        </div>
                    `;

          // 缓存元素引用
          this.cacheElements();

          // 绑定事件
          this.bindEvents();

          // 加载设置
          this.loadSettings();

          // 启动更新循环
          this.startUpdateLoop();

          // 检查存储空间
          this.checkStorageSpace();

          this.initialized = true;
          AriaController.logger.info("GUI initialized successfully");
        } catch (e) {
          AriaController.logger.error("Failed to create GUI:", e);
        }
      }

      cacheElements() {
        this.elements = {
          icon: this.shadowRoot.getElementById("aria-icon"),
          panel: this.shadowRoot.getElementById("aria-panel"),
          sessionSelector: this.shadowRoot.getElementById("session-selector"),
          status: this.shadowRoot.getElementById("status"),
          currentSpeed: this.shadowRoot.getElementById("current-speed"),
          videoSize: this.shadowRoot.getElementById("video-size"),
          audioSize: this.shadowRoot.getElementById("audio-size"),
          duration: this.shadowRoot.getElementById("duration"),
          dlVideo: this.shadowRoot.getElementById("dl-video"),
          dlAudio: this.shadowRoot.getElementById("dl-audio"),
          dlValidate: this.shadowRoot.getElementById("dl-validate"),
          maxSpeedInput: this.shadowRoot.getElementById("max-speed-input"),
          stallNoticeContainer: this.shadowRoot.getElementById(
            "stall-notice-container",
          ),
          storageWarningContainer: this.shadowRoot.getElementById(
            "storage-warning-container",
          ),
          toggles: {
            enableSpeedControl: this.shadowRoot.querySelector(
              '[data-setting="enableSpeedControl"]',
            ),
            autoDownload: this.shadowRoot.querySelector(
              '[data-setting="autoDownload"]',
            ),
            debugMode: this.shadowRoot.querySelector(
              '[data-setting="debugMode"]',
            ),
          },
        };
      }

      bindEvents() {
        // 主图标点击
        this.elements.icon.addEventListener("click", (e) => {
          e.stopPropagation();
          this.elements.panel.classList.toggle("show");
        });

        // 点击外部关闭面板
        document.addEventListener("click", (e) => {
          if (!this.shadowHost.contains(e.target)) {
            this.elements.panel.classList.remove("show");
          }
        });

        // 会话选择
        this.elements.sessionSelector.addEventListener("change", (e) =>
          this.selectSession(e.target.value),
        );

        // 下载按钮（GUI负责检查）
        this.elements.dlVideo.addEventListener("click", () => {
          const session = this.controller.sessions.get(this.currentSessionId);
          if (!session) {
            alert("会话不存在");
            return;
          }
          if (!session.preparedBlobs.video) {
            alert("视频文件尚未准备就绪，请稍候...");
            return;
          }
          this.controller.downloadSession(this.currentSessionId, "video");
        });

        this.elements.dlAudio.addEventListener("click", () => {
          const session = this.controller.sessions.get(this.currentSessionId);
          if (!session) {
            alert("会话不存在");
            return;
          }
          if (!session.preparedBlobs.audio) {
            alert("音频文件尚未准备就绪，请稍候...");
            return;
          }
          this.controller.downloadSession(this.currentSessionId, "audio");
        });

        this.elements.dlValidate.addEventListener("click", () => {
          const session = this.controller.sessions.get(this.currentSessionId);
          if (!session) {
            alert("会话不存在");
            return;
          }
          if (!session.preparedBlobs.video && !session.preparedBlobs.audio) {
            alert("文件尚未准备就绪，请稍候...");
            return;
          }
          this.controller.downloadSession(this.currentSessionId, "both");
        });

        // 设置开关
        Object.entries(this.elements.toggles).forEach(([key, el]) => {
          el.addEventListener("click", () => {
            const isActive = el.classList.toggle("active");
            this.controller.updateSetting(key, isActive);
          });
        });

        // 速度输入
        this.elements.maxSpeedInput.addEventListener("change", (e) => {
          const value = parseFloat(e.target.value);
          if (value >= 1 && value <= 16) {
            this.controller.updateSetting("maxSpeed", value);
          }
        });
      }

      loadSettings() {
        const settings = this.controller.settings;

        for (const key in this.elements.toggles) {
          if (settings[key]) {
            this.elements.toggles[key].classList.add("active");
          }
        }

        this.elements.maxSpeedInput.value = settings.maxSpeed;
      }

      async checkStorageSpace() {
        const storage = await this.controller.dbManager.checkStorageUsage();

        if (storage && storage.percent > 90) {
          this.elements.storageWarningContainer.innerHTML = `
                        <div class="storage-warning">
                            存储空间不足！已使用 ${storage.percent.toFixed(1)}%
                        </div>
                    `;
        }
      }

      updateSessionList() {
        const sessions = Array.from(this.controller.sessions.values());

        if (sessions.length > 1) {
          this.elements.sessionSelector.style.display = "block";
          const currentId = this.currentSessionId;

          this.elements.sessionSelector.innerHTML = sessions
            .map(
              (s, i) =>
                `<option value="${s.id}">会话 ${i + 1} ${s.state === "stalled" ? "(已暂停)" : ""}</option>`,
            )
            .join("");

          if (currentId) {
            this.elements.sessionSelector.value = currentId;
          }
        } else {
          this.elements.sessionSelector.style.display = "none";
        }
      }

      selectSession(sessionId) {
        if (!sessionId || !this.controller.sessions.has(sessionId)) return;

        this.currentSessionId = sessionId;
        this.updateStatus();

        // 清理旧的提示
        this.elements.stallNoticeContainer.innerHTML = "";
        this.elements.storageWarningContainer.innerHTML = "";
      }

      updateStatus() {
        const session = this.controller.sessions.get(this.currentSessionId);
        if (!session) return;

        const statusMap = {
          capturing: "正在捕获...",
          complete: "捕获完成",
          error: "错误",
          stalled: "已暂停",
        };

        this.elements.status.textContent = statusMap[session.state] || "未知";
        this.elements.status.className = `status-value ${session.state}`;

        if (session.videoElement && !session.videoElement.paused) {
          this.elements.currentSpeed.textContent = `${session.videoElement.playbackRate.toFixed(2)}x`;
        } else {
          this.elements.currentSpeed.textContent = "暂停";
        }

        this.elements.videoSize.textContent = `${(session.capturedSize.video / 1024 / 1024).toFixed(2)} MB`;
        this.elements.audioSize.textContent = `${(session.capturedSize.audio / 1024 / 1024).toFixed(2)} MB`;

        const duration = (Date.now() - session.startTime) / 1000;
        this.elements.duration.textContent = this.formatDuration(duration);

        // 更新按钮状态（基于preparedBlobs）
        const hasVideoData = session.capturedSize.video > 0;
        const hasAudioData = session.capturedSize.audio > 0;
        const videoBlobReady = !!session.preparedBlobs.video;
        const audioBlobReady = !!session.preparedBlobs.audio;
        const canDownload = ["complete", "stalled", "error"].includes(
          session.state,
        );

        this.elements.dlVideo.disabled =
          !hasVideoData || !canDownload || !videoBlobReady;
        this.elements.dlAudio.disabled =
          !hasAudioData || !canDownload || !audioBlobReady;
        this.elements.dlValidate.disabled =
          (!hasVideoData && !hasAudioData) ||
          !canDownload ||
          (!videoBlobReady && !audioBlobReady);

        // 更新按钮文字提示准备状态
        if (hasVideoData && canDownload && !videoBlobReady) {
          this.elements.dlVideo.textContent = "准备中...";
        } else {
          this.elements.dlVideo.textContent = "下载视频";
        }

        if (hasAudioData && canDownload && !audioBlobReady) {
          this.elements.dlAudio.textContent = "准备中...";
        } else {
          this.elements.dlAudio.textContent = "下载音频";
        }
      }

      formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
          return `${hours}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
        } else {
          return `${minutes}:${secs.toString().padStart(2, "0")}`;
        }
      }

      startUpdateLoop() {
        if (this.updateInterval) return;

        this.updateInterval = setInterval(() => {
          if (this.currentSessionId) {
            this.updateStatus();
          }
        }, 1000);
      }

      showCompletionNotice() {
        let notice = this.shadowRoot.querySelector(".completion-notice");
        if (notice) notice.remove();

        notice = document.createElement("div");
        notice.className = "completion-notice";
        notice.textContent = "捕获完成，视频已暂停！";
        this.shadowRoot.appendChild(notice);

        setTimeout(() => notice.remove(), 4000);
      }

      showStalledState(sessionId) {
        if (sessionId !== this.currentSessionId) return;

        this.elements.stallNoticeContainer.innerHTML = `
                    <div class="stall-notice">
                        检测到缓冲停滞，捕获已暂停。点击下方按钮刷新页面并继续捕获。
                    </div>
                    <button class="btn btn-resume" id="btn-resume">断点续传</button>
                `;

        const resumeBtn = this.shadowRoot.getElementById("btn-resume");
        resumeBtn.addEventListener("click", () => {
          // 只负责转发事件给控制器
          this.controller.resumeSession(sessionId);
        });

        // 展开面板
        this.elements.panel.classList.add("show");
      }

      showResumePrompt() {
        if (!this.elements.panel.classList.contains("show")) {
          this.elements.panel.classList.add("show");
        }

        // 显示提示让用户手动点击播放
        const notice = document.createElement("div");
        notice.className = "stall-notice";
        notice.textContent = "请点击视频播放按钮继续捕获";
        notice.style.background = "#60a5fa";
        notice.style.color = "#fff";

        this.elements.stallNoticeContainer.appendChild(notice);

        setTimeout(() => notice.remove(), 5000);
      }
    }

    // 主控制器 - v3.2.0 时序优化版
    class AriaController {
      constructor() {
        this.logger = new Logger();
        this.dbManager = new DBManager();
        this.hijackManager = new HijackManager();
        this.heartbeatManager = new HeartbeatManager(); // 无状态的心跳管理器
        this.guiManager = new GUIManager(this);
        this.sessions = new Map(); // 唯一的状态源
        this.initialized = false;
        this.settings = {};
        this.cleanupInterval = null;
        this.heartbeatInterval = null;
      }

      static getInstance() {
        if (!AriaController.instance) {
          AriaController.instance = new AriaController();
        }
        return AriaController.instance;
      }

      loadSettings() {
        this.settings = {
          enableSpeedControl: GM_getValue("enableSpeedControl", true),
          maxSpeed: GM_getValue("maxSpeed", ARIA_USER_CONFIG.DEFAULT_MAX_SPEED),
          autoDownload: GM_getValue("autoDownload", false),
          debugMode: GM_getValue("debugMode", false),
        };

        this.logger.setLevel(this.settings.debugMode ? "debug" : "info");
      }

      updateSetting(key, value) {
        this.settings[key] = value;
        GM_setValue(key, value);

        if (key === "debugMode") {
          this.logger.setLevel(value ? "debug" : "info");
        }

        // 实时更新所有会话的设置
        this.sessions.forEach((session) => {
          if (session.speedController) {
            if (key === "enableSpeedControl") {
              value
                ? session.speedController.start()
                : session.speedController.stop();
            }
            if (key === "maxSpeed") {
              session.speedController.updateMaxSpeed(value);
            }
          }
        });
      }

      async init() {
        if (this.initialized) return;

        try {
          this.logger.info("Initializing AriaController...");

          this.loadSettings();

          // 重要：先初始化UI
          this.guiManager.init();

          // 然后初始化数据库
          await this.dbManager.init();

          // v3.4.2 修复：移除启动时的激进清理，避免多标签页冲突
          // await this._performCleanup(); // 注释掉这行！

          // 劫持MediaSource
          this.hijackManager.init();

          // v3.4.3 增强：监控传统视频元素
          this.monitorTraditionalVideo();

          this.initialized = true;

          this.logger.info("AriaController initialized successfully");

          // 检查是否是恢复会话
          this.checkForResume();

          // 启动定期任务（延迟启动，避免冲突）
          setTimeout(() => {
            this.startPeriodicTasks();
          }, 30000); // 30秒后再启动定期任务
        } catch (error) {
          this.logger.error("Failed to initialize AriaController:", error);
        }
      }

      startPeriodicTasks() {
        // 定期清理（每5分钟）
        this.cleanupInterval = setInterval(() => {
          this._performCleanup();
        }, ARIA_USER_CONFIG.CLEANUP_INTERVAL);

        // 统一的心跳更新（每5秒）
        this.heartbeatInterval = setInterval(() => {
          this.updateAllHeartbeats();
        }, ARIA_USER_CONFIG.HEARTBEAT_INTERVAL);

        // 页面卸载时停止
        window.addEventListener("beforeunload", () => {
          if (this.cleanupInterval) clearInterval(this.cleanupInterval);
          if (this.heartbeatInterval) clearInterval(this.heartbeatInterval);

          // 移除所有会话的心跳
          for (const session of this.sessions.values()) {
            this.heartbeatManager.removeSession(session.id);
          }
        });
      }

      updateAllHeartbeats() {
        // 遍历内存中的会话，更新心跳
        for (const session of this.sessions.values()) {
          if (session.state === "capturing" || session.state === "stalled") {
            this.heartbeatManager.updateSession(
              session.id,
              session.getHeartbeatData(),
            );
          }
        }
      }

      updateSessionHeartbeat(sessionId, sessionData) {
        // 单个会话的心跳更新
        this.heartbeatManager.updateSession(sessionId, sessionData);
      }

      // 统一的清理入口
      async _performCleanup() {
        this.logger.info("Performing cleanup...");

        try {
          // 1. 获取所有数据
          const dbSessionIds = await this.dbManager.getAllSessionIds();
          const activeSessions = this.heartbeatManager.getActiveSessions();
          const activeIds = Object.keys(activeSessions);
          const protectedId = new URLSearchParams(window.location.search).get(
            "aria_resume_id",
          );
          const memoryIds = Array.from(this.sessions.keys());

          // 2. 计算需要清理的孤儿会话
          const orphanedIds = dbSessionIds.filter(
            (id) =>
              !activeIds.includes(id) &&
              !memoryIds.includes(id) &&
              id !== protectedId,
          );

          // 3. 执行数据库清理
          for (const id of orphanedIds) {
            await this.dbManager.deleteSession(id);
            this.logger.debug(`Cleaned orphaned session from DB: ${id}`);
          }

          // 4. 清理内存中的过期会话
          const now = Date.now();
          const sessionsToRemove = [];

          for (const [id, session] of this.sessions) {
            if (session.state === "complete" || session.state === "error") {
              const elapsed = now - session.startTime;
              if (elapsed > ARIA_USER_CONFIG.SESSION_EXPIRE_TIME) {
                sessionsToRemove.push(id);
              }
            }
          }

          for (const id of sessionsToRemove) {
            const session = this.sessions.get(id);
            if (session) {
              session.cleanup();
              this.sessions.delete(id);
              this.heartbeatManager.removeSession(id);
              this.logger.debug(`Cleaned expired session from memory: ${id}`);
            }
          }

          // 5. 清理localStorage中的过期心跳
          const cleanedExpired = this.heartbeatManager.cleanupExpiredSessions();
          if (cleanedExpired) {
            this.logger.debug("Cleaned expired heartbeats from localStorage");
          }

          // 6. 更新UI
          if (sessionsToRemove.length > 0) {
            this.guiManager.updateSessionList();
          }

          this.logger.info(
            `Cleanup completed. Cleaned ${orphanedIds.length} orphaned DB sessions and ${sessionsToRemove.length} expired memory sessions`,
          );
        } catch (e) {
          this.logger.error("Cleanup failed:", e);
        }
      }

      requestCleanup() {
        // 可以被其他组件调用来请求立即清理
        this._performCleanup();
      }

      checkForResume() {
        const urlParams = new URLSearchParams(window.location.search);
        const resumeId = urlParams.get("aria_resume_id");
        const resumeTime = urlParams.get("aria_resume_time");

        if (resumeId && resumeTime) {
          this.logger.info(
            `Resume detected: session=${resumeId}, time=${resumeTime}`,
          );

          // 使用MutationObserver等待视频元素
          const observer = new MutationObserver((mutations, obs) => {
            const videos = Array.from(
              document.querySelectorAll("video"),
            ).filter((v) => v.src && v.src.startsWith("blob:"));

            if (videos.length > 0) {
              obs.disconnect();

              const video = videos[0];

              // 等待视频metadata加载
              const performResume = () => {
                try {
                  video.currentTime = parseFloat(resumeTime);

                  // 尝试自动播放
                  video
                    .play()
                    .then(() => {
                      this.logger.info("Resume playback successful");
                    })
                    .catch((e) => {
                      this.logger.error("Failed to resume playback:", e);

                      // 需要用户交互
                      if (e.name === "NotAllowedError") {
                        this.guiManager.showResumePrompt();
                      }
                    });
                } catch (e) {
                  this.logger.error("Resume error:", e);
                }
              };

              if (video.readyState >= 1) {
                performResume();
              } else {
                video.addEventListener("loadedmetadata", performResume, {
                  once: true,
                });
              }
            }
          });

          observer.observe(document.body, {
            childList: true,
            subtree: true,
          });

          // 超时保护
          setTimeout(() => {
            observer.disconnect();
            this.logger.warn("Resume timeout - no video element found");
          }, 10000);
        }
      }

      // v3.5.0 智能混合版：融合v3.3.0礼貌激活机制与v3.4.3多标签页修复
      registerSession(session) {
        this.logger.info(`Registering session ${session.id}`);
        this.sessions.set(session.id, session);

        // 添加到心跳管理器
        this.heartbeatManager.updateSession(
          session.id,
          session.getHeartbeatData(),
        );

        this.guiManager.selectSession(session.id);
        this.guiManager.updateSessionList();

        // v3.5.0 核心：智能MutationObserver + 礼貌激活延迟
        const observer = new MutationObserver((mutations, obs) => {
          // 每次DOM变化都尝试查找视频元素
          const videoElement = this._findVideoElement();
          if (videoElement) {
            // 找到视频元素后立即断开观察器
            obs.disconnect();
            this.logger.info(
              `MutationObserver successfully found video element for session ${session.id}`,
            );

            // v3.3.0 核心改动：引入"礼貌激活"延迟
            setTimeout(() => {
              this._activateSessionForVideo(session, videoElement);
            }, ARIA_USER_CONFIG.ACTIVATION_DELAY);
          }
        });

        // 立即尝试查找视频元素
        const videoElement = this._findVideoElement();
        if (videoElement) {
          // v3.3.0 核心改动：引入"礼貌激活"延迟
          setTimeout(() => {
            this._activateSessionForVideo(session, videoElement);
          }, ARIA_USER_CONFIG.ACTIVATION_DELAY);
        } else {
          // 如果没有找到，启动 MutationObserver
          this.logger.info(
            `Starting MutationObserver for session ${session.id}`,
          );

          // 配置观察器：监视整个 document.body 的变化
          observer.observe(document.body, {
            childList: true, // 监视子节点的增删
            subtree: true, // 监视所有后代节点
            attributeFilter: ["src"], // 监视 src 属性的变化
          });

          // 安全超时：防止观察器在没有视频的页面上永久运行
          setTimeout(() => {
            observer.disconnect();
            this.logger.warn(
              `Video element association timed out for session ${session.id}`,
            );
          }, ARIA_USER_CONFIG.VIDEO_ELEMENT_TIMEOUT);
        }
      }

      // v3.5.0 智能查找：纯查找功能，职责单一
      _findVideoElement() {
        try {
          const allVideos = Array.from(document.querySelectorAll("video"));
          this.logger.debug(`Found ${allVideos.length} total video elements`);

          const blobVideos = allVideos.filter((v) => {
            const hasSrc = v.src && typeof v.src === "string";
            const isBlob = hasSrc && v.src.startsWith("blob:");
            this.logger.debug(
              `Video element: src="${v.src || "none"}", readyState=${v.readyState}, isBlob=${isBlob}`,
            );
            return isBlob;
          });

          if (blobVideos.length > 0) {
            // 优先选择正在播放的视频
            const playingVideo = blobVideos.find(
              (v) => !v.paused && v.readyState > 2,
            );
            const selectedVideo =
              playingVideo ||
              blobVideos.find((v) => v.readyState > 0) ||
              blobVideos[0];

            this.logger.debug(
              `Selected video: readyState=${selectedVideo.readyState}, paused=${selectedVideo.paused}`,
            );
            return selectedVideo;
          }

          return null;
        } catch (e) {
          this.logger.error("Error in _findVideoElement:", e);
          return null;
        }
      }

      // v3.5.0 智能激活：专门负责激活会话与视频元素的交互
      _activateSessionForVideo(session, videoElement) {
        try {
          session.videoElement = videoElement;

          this.logger.info(
            `Activated session ${session.id} with video element (智能礼貌激活)`,
          );
          this.logger.debug(
            `Video details: readyState=${videoElement.readyState}, currentTime=${videoElement.currentTime}, duration=${videoElement.duration}`,
          );

          // 附加速度控制器
          this.attachSpeedController(session);

          // 更新UI状态
          this.guiManager.updateStatus();
        } catch (e) {
          this.logger.error(
            `Error in _activateSessionForVideo for ${session.id}:`,
            e,
          );
        }
      }

      // v3.4.3 新增：监控传统视频播放（非MediaSource）
      monitorTraditionalVideo() {
        this.logger.info("Setting up traditional video monitoring...");

        // 监控所有视频元素的 src 变化
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (
              mutation.type === "attributes" &&
              mutation.attributeName === "src"
            ) {
              const video = mutation.target;
              if (video.tagName === "VIDEO" && video.src) {
                this.logger.info(
                  `Video src changed: ${video.src.substring(0, 100)}...`,
                );

                // 检查是否是YouTube的直接视频URL
                if (
                  video.src.includes("googlevideo.com") ||
                  video.src.includes("youtube.com")
                ) {
                  this.logger.warn(
                    "Detected traditional YouTube video playback - MediaSource bypass detected!",
                  );
                  this.logger.info(`Full URL: ${video.src}`);
                }
              }
            }

            // 监控新增的视频元素
            if (mutation.type === "childList") {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  const videos =
                    node.tagName === "VIDEO"
                      ? [node]
                      : node.querySelectorAll
                        ? Array.from(node.querySelectorAll("video"))
                        : [];
                  videos.forEach((video) => {
                    this.logger.info(
                      `New video element detected: ${video.src || "no src yet"}`,
                    );

                    // 监听 loadstart 事件
                    video.addEventListener("loadstart", () => {
                      this.logger.info(
                        `Video loadstart: ${video.src.substring(0, 100)}...`,
                      );
                    });

                    // 监听 canplay 事件
                    video.addEventListener("canplay", () => {
                      this.logger.info(
                        `Video canplay: ${video.src.substring(0, 100)}...`,
                      );
                    });
                  });
                }
              });
            }
          });
        });

        observer.observe(document.body || document.documentElement, {
          childList: true,
          subtree: true,
          attributes: true,
          attributeFilter: ["src"],
        });

        // 检查已存在的视频元素
        setTimeout(() => {
          const existingVideos = Array.from(document.querySelectorAll("video"));
          this.logger.info(
            `Found ${existingVideos.length} existing video elements`,
          );
          existingVideos.forEach((video, index) => {
            this.logger.info(
              `Video ${index + 1}: src="${video.src || "none"}", readyState=${video.readyState}`,
            );
          });
        }, 1000);
      }

      // v3.2.0 核心重构：纯事件驱动的速度控制器附加
      attachSpeedController(session) {
        if (!session.videoElement) return;

        const onPlay = () => {
          // 只在明确的播放事件时启动速度控制器
          if (
            this.settings.enableSpeedControl &&
            session.state === "capturing"
          ) {
            if (!session.speedController) {
              session.speedController = new AdaptiveSpeedController(session);
            }
            session.speedController.start();
            this.logger.info(
              `Speed controller started on play event for session ${session.id}`,
            );
          }
        };

        const onPause = () => {
          if (session.speedController) {
            session.speedController.stop();
            this.logger.info(
              `Speed controller stopped on pause event for session ${session.id}`,
            );
          }
        };

        // 使用session的事件管理器
        session.addEventListener(session.videoElement, "play", onPlay);
        session.addEventListener(session.videoElement, "pause", onPause);

        // 检查当前状态，处理页面加载时视频就自动播放的情况
        if (
          !session.videoElement.paused &&
          session.videoElement.readyState >= 2
        ) {
          // 视频已经在播放，立即调用 play 回调
          onPlay();
        }
      }

      onSessionComplete(session) {
        // 更新心跳状态
        this.heartbeatManager.updateSession(
          session.id,
          session.getHeartbeatData(),
        );

        this.guiManager.showCompletionNotice();

        if (session.videoElement && !session.videoElement.paused) {
          try {
            session.videoElement.pause();
            this.logger.info(`Session ${session.id} complete. Video paused.`);
          } catch (e) {
            this.logger.error("Failed to pause video:", e);
          }
        }

        if (this.settings.autoDownload) {
          this.downloadSession(session.id, "both");
        }
      }

      // 断点续传逻辑
      resumeSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) return;

        const currentTime = session.videoElement?.currentTime || 0;
        const url = new URL(window.location.href);

        url.searchParams.set("aria_resume_id", sessionId);
        url.searchParams.set("aria_resume_time", currentTime.toString());

        window.location.href = url.toString();
      }

      // 完全同步的下载方法（最终版）
      downloadSession(sessionId, type) {
        const session = this.sessions.get(sessionId);
        if (!session) {
          this.logger.error(`Session ${sessionId} not found`);
          return;
        }

        // 只使用预准备的Blob URLs（完全同步）
        const download = (blobUrl, filename, fileType) => {
          if (!blobUrl) {
            this.logger.warn(`No blob URL available for ${filename}`);
            return;
          }

          const a = document.createElement("a");
          a.href = blobUrl;
          a.download = filename;
          a.style.display = "none";

          document.body.appendChild(a);

          try {
            a.click();
            this.logger.info(`Download triggered for ${filename}`);

            // 计划释放Blob URL
            session.scheduleBlobUrlRevoke(fileType, blobUrl);
          } catch (e) {
            this.logger.error("Download failed:", e);
          }

          // 快速移除DOM元素
          setTimeout(() => {
            document.body.removeChild(a);
          }, 100);
        };

        // 生成安全的文件名
        const safeFilename = (document.title || "video")
          .replace(/[\\/:*?"<>|]/g, "-")
          .substring(0, 100);

        // 立即连续触发下载
        if (type === "video" || type === "both") {
          const videoUrl = session.preparedBlobs.video;
          if (videoUrl) {
            const ext = session.fileExtensions.video;
            download(videoUrl, `${safeFilename}_video${ext}`, "video");
          }
        }

        if (type === "audio" || type === "both") {
          const audioUrl = session.preparedBlobs.audio;
          if (audioUrl) {
            const ext = session.fileExtensions.audio;
            download(audioUrl, `${safeFilename}_audio${ext}`, "audio");
          }
        }
      }
    }

    AriaController.logger = new Logger();
    return AriaController;
  })();

  // 启动脚本
  (async function () {
    try {
      // 检查浏览器兼容性
      if (!window.MediaSource || typeof MediaSource !== "function") {
        console.warn("[Aria] MediaSource API not available");
        return;
      }

      if (!window.indexedDB) {
        console.error("[Aria] IndexedDB not available");
        return;
      }

      if (!window.Blob || !window.URL || !URL.createObjectURL) {
        console.error("[Aria] Required APIs not available");
        return;
      }

      // 等待基本DOM结构
      if (document.readyState === "loading") {
        await new Promise((resolve) => {
          const checkReady = () => {
            if (document.readyState !== "loading") {
              resolve();
            } else {
              setTimeout(checkReady, 10);
            }
          };
          checkReady();
        });
      }

      // 初始化
      await AriaDownloader.getInstance().init();

      console.log("[Aria] Ready to capture media");
    } catch (error) {
      console.error("[Aria] Fatal error during bootstrap:", error);
    }
  })();
})();
