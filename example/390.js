// ==UserScript==
// @name         Aria Downloader 媒体捕获工具 (主动续传版)
// @namespace    aria-downloader.io
// @version      3.9.0
// @description  智能媒体捕获工具，增强断点续传机制，支持主动刷新续传和智能会话恢复
// <AUTHOR> Project (Production Build)
// @match        *://*.youtube.com/*
// @match        *://*.youtube-nocookie.com/*
// @match        *://*.bilibili.com/*
// @match        *://*.vimeo.com/*
// @match        *://*.netflix.com/*
// @match        *://*.twitch.tv/*
// @match        *://*.hulu.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_deleteValue
// @grant        GM_listValues
// @run-at       document-start
// ==/UserScript==

console.log("[Aria Bootstrapper] Script injected successfully. Version 3.9.0");

// 用户可配置参数
const ARIA_USER_CONFIG = {
  SPEED_HIGH_WATERMARK: 45,
  SPEED_LOW_WATERMARK: 15,
  SPEED_SMOOTHING_FACTOR: 0.1,
  DEFAULT_MAX_SPEED: 8.0,
  HEARTBEAT_INTERVAL: 5000,
  HEARTBEAT_TIMEOUT: 60000, // v3.4.2 修复：增加到60秒，避免多标签页误杀
  STALL_CHECK_INTERVAL: 2000,
  STALL_THRESHOLD: 5,
  MAX_CAPTURE_SIZE_MB: 2048, // v3.6.2 紧急修复：降低到2GB
  STORAGE_WARNING_THRESHOLD: 0.85, // v3.8.0 用户可配置：85%时警告
  STORAGE_CRITICAL_THRESHOLD: 0.9, // v3.8.0 用户可配置：90%时强制清理
  AUTO_CLEANUP_ENABLED: true, // v3.6.2 新增：启用自动清理
  CHUNK_BATCH_SIZE: 50, // 批处理大小
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  CLEANUP_INTERVAL: 600000, // v3.4.2 修复：增加到10分钟清理一次，减少冲突
  SESSION_EXPIRE_TIME: 1800000, // v3.4.2 修复：增加到30分钟后过期
  BLOB_URL_REVOKE_DELAY: 15000, // 15秒后释放Blob URL
  VIDEO_ELEMENT_TIMEOUT: 20000, // 20秒视频元素检测超时
  ACTIVATION_DELAY: 750, // 礼貌激活延迟（毫秒）
};

(function () {
  "use strict";

  // v3.2.0: 添加iframe检测，确保只在顶层页面执行
  if (window.self !== window.top) {
    console.log("[Aria] Script blocked in iframe");
    return;
  }

  const AriaDownloader = (() => {
    // 日志管理器
    class Logger {
      constructor() {
        this.level = "info";
      }

      setLevel(level) {
        this.level = level;
        this.info(`Log level set to: ${level}`);
      }

      log(level, ...args) {
        const levels = { none: 0, error: 1, info: 2, debug: 3 };
        if (levels[level] <= levels[this.level]) {
          console.log(`[Aria ${level.toUpperCase()}]`, ...args);
        }
      }

      error(...args) {
        this.log("error", ...args);
      }
      info(...args) {
        this.log("info", ...args);
      }
      debug(...args) {
        this.log("debug", ...args);
      }
    }

    // 心跳管理器 - 无状态版本
    class HeartbeatManager {
      constructor() {
        this.heartbeatKey = "aria_active_sessions";
      }

      // 直接操作localStorage，不维护内部状态
      updateSession(sessionId, sessionData) {
        try {
          const heartbeats = this.getAllStoredSessions();
          heartbeats[sessionId] = {
            timestamp: Date.now(),
            url: window.location.href,
            ...sessionData,
          };
          localStorage.setItem(this.heartbeatKey, JSON.stringify(heartbeats));
        } catch (e) {
          AriaController.logger.error("Failed to update heartbeat:", e);
          if (e.name === "QuotaExceededError") {
            AriaController.getInstance().requestCleanup();
          }
        }
      }

      removeSession(sessionId) {
        try {
          const heartbeats = this.getAllStoredSessions();
          delete heartbeats[sessionId];
          localStorage.setItem(this.heartbeatKey, JSON.stringify(heartbeats));
        } catch (e) {
          AriaController.logger.error("Failed to remove heartbeat:", e);
        }
      }

      // 返回所有活跃会话（时间戳未过期的）
      getActiveSessions() {
        const now = Date.now();
        const allSessions = this.getAllStoredSessions();
        const active = {};

        for (const [id, data] of Object.entries(allSessions)) {
          if (now - data.timestamp < ARIA_USER_CONFIG.HEARTBEAT_TIMEOUT) {
            active[id] = data;
          }
        }

        return active;
      }

      // 获取所有存储的会话（包括过期的）
      getAllStoredSessions() {
        try {
          return JSON.parse(localStorage.getItem(this.heartbeatKey) || "{}");
        } catch (e) {
          AriaController.logger.error("Failed to parse heartbeats:", e);
          return {};
        }
      }

      // 清理过期的会话数据
      cleanupExpiredSessions() {
        const now = Date.now();
        const allSessions = this.getAllStoredSessions();
        const cleaned = {};
        let hasChanges = false;

        for (const [id, data] of Object.entries(allSessions)) {
          if (now - data.timestamp < ARIA_USER_CONFIG.HEARTBEAT_TIMEOUT * 2) {
            cleaned[id] = data;
          } else {
            hasChanges = true;
          }
        }

        if (hasChanges) {
          try {
            localStorage.setItem(this.heartbeatKey, JSON.stringify(cleaned));
          } catch (e) {
            AriaController.logger.error("Failed to cleanup heartbeats:", e);
          }
        }

        return hasChanges;
      }
    }

    // 数据库管理器
    class DBManager {
      constructor() {
        this.dbName = "AriaDownloaderDB_v3";
        this.version = 1;
        this.db = null;
      }

      async init() {
        try {
          await this.openDatabase();
        } catch (error) {
          if (
            error.name === "VersionError" ||
            error.name === "InvalidStateError"
          ) {
            AriaController.logger.error(
              "DB error detected. Attempting recovery...",
            );
            if (this.db) this.db.close();
            await this.destroyDatabase();
            AriaController.logger.info(
              "Corrupted DB deleted. Retrying initialization.",
            );
            await this.openDatabase();
          } else {
            throw error;
          }
        }
      }

      async destroyDatabase() {
        return new Promise((resolve, reject) => {
          const req = indexedDB.deleteDatabase(this.dbName);
          req.onsuccess = resolve;
          req.onerror = reject;
          req.onblocked = () => {
            AriaController.logger.warn("DB deletion blocked, waiting...");
            setTimeout(resolve, 1000);
          };
        });
      }

      async openDatabase() {
        return new Promise((resolve, reject) => {
          const request = indexedDB.open(this.dbName, this.version);

          request.onerror = (e) => {
            AriaController.logger.error(
              "Failed to open database:",
              e.target.error,
            );
            reject(e.target.error);
          };

          request.onsuccess = (e) => {
            this.db = e.target.result;

            this.db.onerror = (event) => {
              AriaController.logger.error(
                "Database error:",
                event.target.error,
              );
            };

            resolve();
          };

          request.onupgradeneeded = (e) => {
            const db = e.target.result;

            if (db.objectStoreNames.contains("chunks")) {
              db.deleteObjectStore("chunks");
            }

            const store = db.createObjectStore("chunks", {
              keyPath: "id",
              autoIncrement: true,
            });
            store.createIndex("sessionId", "sessionId", { unique: false });
            store.createIndex("type", "type", { unique: false });
            store.createIndex("timestamp", "timestamp", { unique: false });
          };
        });
      }

      // 只返回所有会话ID，不做任何清理决策
      async getAllSessionIds() {
        if (!this.db) return [];

        const transaction = this.db.transaction(["chunks"], "readonly");
        const store = transaction.objectStore("chunks");
        const index = store.index("sessionId");

        return new Promise((resolve, reject) => {
          const request = index.getAllKeys();
          request.onsuccess = () => {
            const uniqueIds = [...new Set(request.result)];
            resolve(uniqueIds);
          };
          request.onerror = (e) => reject(e.target.error);
        });
      }

      async deleteSession(sessionId) {
        if (!this.db) return;

        const transaction = this.db.transaction(["chunks"], "readwrite");
        const store = transaction.objectStore("chunks");
        const index = store.index("sessionId");

        return new Promise((resolve, reject) => {
          const request = index.openCursor(IDBKeyRange.only(sessionId));

          request.onsuccess = (e) => {
            const cursor = e.target.result;
            if (cursor) {
              cursor.delete();
              cursor.continue();
            } else {
              resolve();
            }
          };

          request.onerror = reject;
        });
      }

      async saveChunk(sessionId, type, data) {
        if (!this.db) throw new Error("Database not initialized");

        const transaction = this.db.transaction(["chunks"], "readwrite");

        return new Promise((resolve, reject) => {
          const request = transaction.objectStore("chunks").add({
            sessionId,
            type,
            data,
            timestamp: Date.now(),
          });

          request.onsuccess = resolve;
          request.onerror = (e) => {
            if (e.target.error.name === "QuotaExceededError") {
              AriaController.logger.error("Storage quota exceeded");
              reject(new Error("QUOTA_EXCEEDED"));
            } else {
              reject(e.target.error);
            }
          };
        });
      }

      async getChunks(sessionId, type) {
        if (!this.db) return [];

        const transaction = this.db.transaction(["chunks"], "readonly");
        const index = transaction.objectStore("chunks").index("sessionId");

        return new Promise((resolve, reject) => {
          const request = index.getAll(sessionId);

          request.onsuccess = () => {
            const chunks = request.result;
            if (type) {
              resolve(
                chunks
                  .filter((c) => c.type === type)
                  .sort((a, b) => a.id - b.id),
              );
            } else {
              resolve(chunks.sort((a, b) => a.id - b.id));
            }
          };

          request.onerror = (e) => reject(e.target.error);
        });
      }

      async checkStorageUsage() {
        if ("storage" in navigator && "estimate" in navigator.storage) {
          try {
            const estimate = await navigator.storage.estimate();
            const usagePercent = (estimate.usage / estimate.quota) * 100;
            return {
              usage: estimate.usage,
              quota: estimate.quota,
              percent: usagePercent,
            };
          } catch (e) {
            AriaController.logger.error("Failed to check storage usage:", e);
          }
        }
        return null;
      }
    }

    // 增强的速度控制器（整合了停滞检测）
    class AdaptiveSpeedController {
      constructor(session) {
        this.session = session;
        this.videoElement = session.videoElement;
        this.animationFrameId = null;
        this.lastBufferCheck = 0;
        this.config = {
          HIGH_WATERMARK: ARIA_USER_CONFIG.SPEED_HIGH_WATERMARK,
          LOW_WATERMARK: ARIA_USER_CONFIG.SPEED_LOW_WATERMARK,
          SMOOTHING: ARIA_USER_CONFIG.SPEED_SMOOTHING_FACTOR,
          MAX_SPEED: ARIA_USER_CONFIG.DEFAULT_MAX_SPEED,
          NORMAL_SPEED: 1.0,
        };

        // 停滞检测相关
        this.stallCount = 0;
        this.lastCurrentTime = 0;
        this.lastBufferedEnd = 0;
        this.lastStallCheck = 0;
      }

      updateMaxSpeed(speed) {
        this.config.MAX_SPEED =
          parseFloat(speed) || ARIA_USER_CONFIG.DEFAULT_MAX_SPEED;
        AriaController.logger.info(
          `Max speed updated to: ${this.config.MAX_SPEED}x`,
        );
      }

      start() {
        if (!this.videoElement || this.animationFrameId) return;

        this.updateMaxSpeed(AriaDownloader.getInstance().settings.maxSpeed);
        AriaController.logger.info(
          `Adaptive speed control started for session ${this.session.id}`,
        );
        this.monitor();
      }

      stop() {
        if (this.animationFrameId) {
          cancelAnimationFrame(this.animationFrameId);
          this.animationFrameId = null;

          if (this.videoElement && !this.videoElement.paused) {
            try {
              this.videoElement.playbackRate = this.config.NORMAL_SPEED;
            } catch (e) {
              AriaController.logger.debug("Failed to reset playback rate:", e);
            }
          }

          AriaController.logger.info(
            `Adaptive speed control stopped for session ${this.session.id}`,
          );
        }
      }

      monitor = () => {
        if (
          !this.videoElement ||
          this.videoElement.paused ||
          this.session.state === "complete"
        ) {
          this.stop();
          return;
        }

        const now = performance.now();

        // 速度调整逻辑（100ms间隔）
        if (now - this.lastBufferCheck > 100) {
          this.lastBufferCheck = now;

          try {
            if (this.videoElement.buffered.length > 0) {
              const bufferEnd = this.videoElement.buffered.end(
                this.videoElement.buffered.length - 1,
              );
              const bufferAhead = bufferEnd - this.videoElement.currentTime;
              let targetRate;

              if (bufferAhead <= this.config.LOW_WATERMARK) {
                targetRate = this.config.NORMAL_SPEED;
              } else if (bufferAhead >= this.config.HIGH_WATERMARK) {
                targetRate = this.config.MAX_SPEED;
              } else {
                const bufferRange =
                  this.config.HIGH_WATERMARK - this.config.LOW_WATERMARK;
                const speedRange =
                  this.config.MAX_SPEED - this.config.NORMAL_SPEED;
                const bufferProgress =
                  (bufferAhead - this.config.LOW_WATERMARK) / bufferRange;
                targetRate =
                  this.config.NORMAL_SPEED + speedRange * bufferProgress;
              }

              const currentRate = this.videoElement.playbackRate;
              const newRate =
                currentRate +
                (targetRate - currentRate) * this.config.SMOOTHING;

              if (Math.abs(newRate - currentRate) > 0.01) {
                this.videoElement.playbackRate = newRate;
              }

              AriaController.logger.debug(
                `Buffer: ${bufferAhead.toFixed(1)}s, Speed: ${newRate.toFixed(2)}x`,
              );
            }
          } catch (e) {
            AriaController.logger.debug("Speed control error:", e);
          }
        }

        // 停滞检测逻辑（每2秒检查一次）
        if (now - this.lastStallCheck > ARIA_USER_CONFIG.STALL_CHECK_INTERVAL) {
          this.lastStallCheck = now;
          this.checkForStall();
        }

        this.animationFrameId = requestAnimationFrame(this.monitor);
      };

      checkForStall() {
        if (
          !this.videoElement ||
          this.session.state !== "capturing" ||
          this.videoElement.paused
        ) {
          return;
        }

        try {
          const currentTime = this.videoElement.currentTime;

          // 检查播放是否停滞
          if (Math.abs(currentTime - this.lastCurrentTime) < 0.1) {
            if (
              !this.videoElement.paused &&
              this.videoElement.readyState >= 2
            ) {
              if (this.videoElement.buffered.length > 0) {
                const bufferedEnd = this.videoElement.buffered.end(
                  this.videoElement.buffered.length - 1,
                );

                if (bufferedEnd > currentTime + 1) {
                  this.stallCount++;

                  if (this.stallCount >= ARIA_USER_CONFIG.STALL_THRESHOLD) {
                    this.handleStall();
                  }
                } else if (bufferedEnd === this.lastBufferedEnd) {
                  this.stallCount++;

                  if (this.stallCount >= ARIA_USER_CONFIG.STALL_THRESHOLD) {
                    this.handleStall();
                  }
                }
              }
            }
          } else {
            this.stallCount = 0;
          }

          this.lastCurrentTime = currentTime;
          if (this.videoElement.buffered.length > 0) {
            this.lastBufferedEnd = this.videoElement.buffered.end(
              this.videoElement.buffered.length - 1,
            );
          }
        } catch (e) {
          AriaController.logger.error("Stall detection error:", e);
        }
      }

      handleStall() {
        AriaController.logger.info(
          `Stall detected for session ${this.session.id}`,
        );
        this.session.markAsStalled();
        AriaController.getInstance().guiManager.showStalledState(
          this.session.id,
        );
        this.stop();
      }
    }

    // 捕获会话 - 优化版
    class CaptureSession {
      constructor(id, mediaSource, isResume = false) {
        this.id = id;
        this.mediaSource = mediaSource;
        this.state = "capturing";
        this.videoElement = null;
        this.capturedSize = { video: 0, audio: 0 };
        this.startTime = Date.now();
        this.dbManager = AriaDownloader.getInstance().dbManager;
        this.speedController = null;
        this.preparedBlobs = { video: null, audio: null };
        this.isResume = isResume;
        this.eventListeners = new Map();
        this.chunkQueue = { video: [], audio: [] };
        this.processingQueue = false;

        // 存储文件扩展名
        this.fileExtensions = { video: ".webm", audio: ".webm" };

        // 存储Blob URL以便后续释放
        this.blobUrlTimers = new Map();

        // 如果是恢复会话，加载之前的状态
        if (isResume) {
          this.loadResumeState();
        }
      }

      loadResumeState() {
        try {
          const controller = AriaDownloader.getInstance();
          const activeSessions =
            controller.heartbeatManager.getActiveSessions();
          const sessionData = activeSessions[this.id];

          if (sessionData) {
            if (sessionData.capturedSize) {
              this.capturedSize = sessionData.capturedSize;
            }
            if (sessionData.startTime) {
              this.startTime = sessionData.startTime;
            }
            if (sessionData.fileExtensions) {
              this.fileExtensions = sessionData.fileExtensions;
            }
            AriaController.logger.info(
              `Resumed session ${this.id} with previous state`,
            );
          }
        } catch (e) {
          AriaController.logger.error("Failed to load resume state:", e);
        }
      }

      setMimeType(type, mimeType) {
        // 解析mime类型以确定文件扩展名
        const mimeToExt = {
          "video/webm": ".webm",
          "video/mp4": ".mp4",
          "video/x-matroska": ".mkv",
          "audio/webm": ".webm",
          "audio/mp4": ".m4a",
          "audio/mpeg": ".mp3",
          "audio/ogg": ".ogg",
        };

        let ext = mimeToExt[mimeType];

        // v3.9.0 增强：动态MIME类型映射
        if (!ext) {
          // 移除分号后的参数部分（如 codecs）
          const cleanMimeType = mimeType.split(";")[0].trim();
          ext = mimeToExt[cleanMimeType];

          if (!ext) {
            // 从MIME类型中提取文件后缀
            const mimeMatch = cleanMimeType.match(/^[^/]+\/(.+)$/);
            if (mimeMatch && mimeMatch[1]) {
              ext = "." + mimeMatch[1];
              AriaController.logger.info(
                `Dynamic MIME mapping: ${cleanMimeType} -> ${ext}`,
              );
            } else {
              // 最终回退
              ext = type === "audio" ? ".webm" : ".webm";
              AriaController.logger.warn(
                `Unknown MIME type ${mimeType}, using fallback: ${ext}`,
              );
            }
          }
        }

        this.fileExtensions[type] = ext;

        AriaController.logger.debug(
          `Set ${type} extension to ${ext} based on mime type: ${mimeType}`,
        );
      }

      getHeartbeatData() {
        return {
          state: this.state,
          capturedSize: this.capturedSize,
          startTime: this.startTime,
          currentTime: this.videoElement?.currentTime || 0,
          fileExtensions: this.fileExtensions,
        };
      }

      async captureData(type, buffer) {
        // 检查存储限制
        const totalSizeMB =
          (this.capturedSize.video + this.capturedSize.audio) / 1024 / 1024;
        if (totalSizeMB >= ARIA_USER_CONFIG.MAX_CAPTURE_SIZE_MB) {
          AriaController.logger.warn(
            `Capture size limit reached: ${totalSizeMB}MB`,
          );
          this.complete();
          return;
        }

        // 添加到队列
        this.chunkQueue[type].push(buffer.slice(0));

        // 批处理队列
        if (!this.processingQueue) {
          this.processChunkQueue();
        }
      }

      async processChunkQueue() {
        this.processingQueue = true;

        try {
          for (const type of ["video", "audio"]) {
            while (this.chunkQueue[type].length > 0) {
              const batch = this.chunkQueue[type].splice(
                0,
                ARIA_USER_CONFIG.CHUNK_BATCH_SIZE,
              );

              for (const chunk of batch) {
                let retries = 0;
                let saved = false;

                while (retries < ARIA_USER_CONFIG.RETRY_ATTEMPTS && !saved) {
                  try {
                    await this.dbManager.saveChunk(this.id, type, chunk);
                    this.capturedSize[type] += chunk.byteLength;
                    saved = true;
                  } catch (e) {
                    retries++;

                    if (e.message === "QUOTA_EXCEEDED") {
                      AriaController.logger.error(
                        "Storage quota exceeded, stopping capture",
                      );
                      this.complete();
                      return;
                    }

                    if (retries < ARIA_USER_CONFIG.RETRY_ATTEMPTS) {
                      AriaController.logger.debug(
                        `Retry ${retries} for chunk save`,
                      );
                      await new Promise((resolve) =>
                        setTimeout(
                          resolve,
                          ARIA_USER_CONFIG.RETRY_DELAY * retries,
                        ),
                      );
                    } else {
                      throw e;
                    }
                  }
                }
              }

              // 通知控制器更新心跳
              AriaDownloader.getInstance().updateSessionHeartbeat(
                this.id,
                this.getHeartbeatData(),
              );
            }
          }
        } catch (e) {
          this.state = "error";
          AriaController.logger.error(
            `Failed to save chunks for session ${this.id}:`,
            e,
          );
        } finally {
          this.processingQueue = false;

          // 如果还有剩余数据，继续处理
          if (
            this.chunkQueue.video.length > 0 ||
            this.chunkQueue.audio.length > 0
          ) {
            setTimeout(() => this.processChunkQueue(), 100);
          }
        }
      }

      markAsStalled() {
        this.state = "stalled";

        const stallData = {
          ...this.getHeartbeatData(),
          stallTime: this.videoElement?.currentTime || 0,
          state: "stalled",
        };

        // 通知控制器更新心跳
        AriaDownloader.getInstance().updateSessionHeartbeat(this.id, stallData);

        if (this.speedController) {
          this.speedController.stop();
        }
      }

      async complete() {
        if (this.state === "complete") return;

        this.state = "complete";

        if (this.speedController) {
          this.speedController.stop();
        }

        // 等待所有队列处理完成
        while (
          this.processingQueue ||
          this.chunkQueue.video.length > 0 ||
          this.chunkQueue.audio.length > 0
        ) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }

        // 预准备Blob URLs
        await this.prepareBlobUrls();

        // 通知控制器
        AriaDownloader.getInstance().onSessionComplete(this);
      }

      async prepareBlobUrls() {
        try {
          AriaController.logger.info(
            `Preparing blob URLs for session ${this.id}`,
          );

          // 视频Blob
          const videoChunks = await this.dbManager.getChunks(this.id, "video");
          if (videoChunks.length > 0) {
            const videoData = videoChunks.map((c) => c.data);
            const videoBlob = new Blob(videoData, { type: "video/webm" });
            this.preparedBlobs.video = URL.createObjectURL(videoBlob);
            AriaController.logger.debug(
              `Video blob prepared: ${videoData.length} chunks`,
            );
          }

          // 音频Blob
          const audioChunks = await this.dbManager.getChunks(this.id, "audio");
          if (audioChunks.length > 0) {
            const audioData = audioChunks.map((c) => c.data);
            const audioBlob = new Blob(audioData, { type: "audio/webm" });
            this.preparedBlobs.audio = URL.createObjectURL(audioBlob);
            AriaController.logger.debug(
              `Audio blob prepared: ${audioData.length} chunks`,
            );
          }

          AriaController.logger.info(
            `Blob URLs prepared for session ${this.id}`,
          );
        } catch (e) {
          AriaController.logger.error(
            `Failed to prepare blob URLs for session ${this.id}:`,
            e,
          );
        }
      }

      // 计划释放Blob URL
      scheduleBlobUrlRevoke(type, url) {
        if (this.blobUrlTimers.has(type)) {
          clearTimeout(this.blobUrlTimers.get(type));
        }

        const timer = setTimeout(() => {
          URL.revokeObjectURL(url);
          this.blobUrlTimers.delete(type);
          AriaController.logger.debug(`Blob URL revoked for ${type}`);
        }, ARIA_USER_CONFIG.BLOB_URL_REVOKE_DELAY);

        this.blobUrlTimers.set(type, timer);
      }

      cleanup() {
        AriaController.logger.info(`Cleaning up session ${this.id}`);

        if (this.speedController) {
          this.speedController.stop();
        }

        // 清除所有定时器
        for (const timer of this.blobUrlTimers.values()) {
          clearTimeout(timer);
        }
        this.blobUrlTimers.clear();

        // 释放Blob URLs
        if (this.preparedBlobs.video) {
          URL.revokeObjectURL(this.preparedBlobs.video);
          this.preparedBlobs.video = null;
        }

        if (this.preparedBlobs.audio) {
          URL.revokeObjectURL(this.preparedBlobs.audio);
          this.preparedBlobs.audio = null;
        }

        // 移除所有事件监听器
        for (const [element, listeners] of this.eventListeners) {
          for (const [event, handler] of listeners) {
            element.removeEventListener(event, handler);
          }
        }
        this.eventListeners.clear();

        // 清空队列
        this.chunkQueue = { video: [], audio: [] };
      }

      addEventListener(element, event, handler) {
        if (!this.eventListeners.has(element)) {
          this.eventListeners.set(element, new Map());
        }
        this.eventListeners.get(element).set(event, handler);
        element.addEventListener(event, handler);
      }
    }

    // 劫持管理器 - v3.2.0 重构版
    class HijackManager {
      constructor() {
        this.originalAddSourceBuffer = MediaSource.prototype.addSourceBuffer;
        this.originalEndOfStream = MediaSource.prototype.endOfStream;
        this.mediaSourceMap = new WeakMap();
        this.hijacked = false;
      }

      init() {
        if (this.hijacked) return;

        const self = this;

        try {
          // v3.4.3 增强：添加详细的劫持日志
          AriaController.logger.info("Starting MediaSource hijack...");

          MediaSource.prototype.addSourceBuffer = function (mime) {
            AriaController.logger.info(
              `MediaSource.addSourceBuffer called with mime: ${mime}`,
            );
            AriaController.logger.debug(`MediaSource instance:`, this);
            AriaController.logger.debug(
              `MediaSource readyState: ${this.readyState}`,
            );

            let session = self.mediaSourceMap.get(this);

            if (!session) {
              // 检查是否是恢复会话
              const urlParams = new URLSearchParams(window.location.search);
              const resumeId = urlParams.get("aria_resume_id");
              const sessionId =
                resumeId ||
                `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

              session = new CaptureSession(sessionId, this, !!resumeId);
              self.mediaSourceMap.set(this, session);

              // 使用setTimeout避免同步调用栈问题
              setTimeout(() => {
                AriaDownloader.getInstance().registerSession(session);
              }, 0);
            }

            const sourceBuffer = self.originalAddSourceBuffer.call(this, mime);
            const originalAppend = sourceBuffer.appendBuffer;
            const type = mime.includes("audio") ? "audio" : "video";

            // 解析并存储mime类型
            session.setMimeType(type, mime);

            sourceBuffer.appendBuffer = function (buffer) {
              if (session.state === "capturing") {
                session.captureData(type, buffer);
              }
              return originalAppend.call(this, buffer);
            };

            // v3.2.0 核心改动：在这里调用 attachSpeedController
            // 确保会话已经有了视频元素后才附加速度控制器
            if (session.videoElement && !session.speedController) {
              AriaDownloader.getInstance().attachSpeedController(session);
            }

            return sourceBuffer;
          };

          MediaSource.prototype.endOfStream = function () {
            const session = self.mediaSourceMap.get(this);
            if (session && session.state === "capturing") {
              session.complete();
            }
            return self.originalEndOfStream.call(this);
          };

          this.hijacked = true;
          AriaController.logger.info("MediaSource hijacked successfully");

          // v3.5.0 增强：更强的 MediaSource 创建监控
          const originalMediaSource = window.MediaSource;
          window.MediaSource = function (...args) {
            const instance = new originalMediaSource(...args);
            AriaController.logger.info("New MediaSource created");
            AriaController.logger.debug(
              `MediaSource instance created:`,
              instance,
            );

            // 监控 MediaSource 状态变化
            const originalAddEventListener = instance.addEventListener;
            instance.addEventListener = function (event, handler, options) {
              if (event === "sourceopen") {
                AriaController.logger.info(
                  "MediaSource sourceopen event listener added",
                );
              }
              return originalAddEventListener.call(
                this,
                event,
                handler,
                options,
              );
            };

            return instance;
          };
          // 保持原型链
          window.MediaSource.prototype = originalMediaSource.prototype;
          Object.setPrototypeOf(window.MediaSource, originalMediaSource);

          // v3.5.0 增强：监控 URL.createObjectURL 调用
          const originalCreateObjectURL = URL.createObjectURL;
          URL.createObjectURL = function (object) {
            const url = originalCreateObjectURL.call(this, object);
            if (object instanceof MediaSource) {
              AriaController.logger.info(
                `MediaSource blob URL created: ${url}`,
              );
            }
            return url;
          };
        } catch (e) {
          AriaController.logger.error("Failed to hijack MediaSource:", e);
        }
      }
    }

    // GUI管理器 - 最终优化版
    class GUIManager {
      constructor(controller) {
        this.controller = controller;
        this.shadowHost = null;
        this.shadowRoot = null;
        this.elements = {};
        this.currentSessionId = null;
        this.updateInterval = null;
        this.initialized = false;
      }

      init() {
        if (this.initialized) return;

        if (document.body) {
          this.createGUI();
        } else {
          const observer = new MutationObserver((mutations, obs) => {
            if (document.body) {
              obs.disconnect();
              this.createGUI();
            }
          });

          observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
          });
        }
      }

      createGUI() {
        if (this.shadowHost) return;

        try {
          this.shadowHost = document.createElement("div");
          this.shadowHost.id = "aria-downloader-host";
          document.body.appendChild(this.shadowHost);

          // 创建Shadow DOM
          this.shadowRoot = this.shadowHost.attachShadow({ mode: "closed" });

          // 注入样式和HTML
          this.shadowRoot.innerHTML = `
                        <style>
                            :host{position:fixed;top:20px;right:20px;z-index:2147483647;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif}
                            .aria-icon{width:48px;height:48px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;opacity:.7;transition:all .3s ease;box-shadow:0 2px 10px rgba(0,0,0,.2)}
                            .aria-icon:hover{opacity:1;transform:scale(1.1)}
                            .aria-icon svg{width:24px;height:24px;fill:#fff}
                            .aria-panel{display:none;position:absolute;top:60px;right:0;width:320px;background-color:#1a1a1a;border-radius:12px;padding:20px;color:#fff;font-size:14px;box-shadow:0 10px 30px rgba(0,0,0,.3)}
                            .aria-panel.show{display:block}
                            .panel-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;padding-bottom:10px;border-bottom:1px solid #333}
                            .panel-title{font-size:18px;font-weight:600}
                            .panel-version{font-size:12px;color:#666}
                            .status-section,.settings-section{background-color:#262626;border-radius:8px;padding:15px;margin-bottom:15px}
                            .status-item{display:flex;justify-content:space-between;margin-bottom:8px;font-size:13px}
                            .status-item:last-child{margin-bottom:0}
                            .status-label{color:#999}
                            .status-value{font-weight:bold;color:#fff}
                            .status-value.capturing{color:#4ade80}
                            .status-value.complete{color:#60a5fa}
                            .status-value.stalled{color:#fbbf24}
                            .status-value.error{color:#ef4444}
                            .action-buttons{display:grid;grid-template-columns:1fr 1fr;gap:10px;margin-bottom:10px}
                            .btn{padding:10px;border:none;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s;color:#fff}
                            .btn-primary{background-color:#4a5568}
                            .btn-primary:not(:disabled):hover{background-color:#2d3748}
                            .btn-danger{background:linear-gradient(135deg,#ff6b6b 0%,#ee5a52 100%);border:1px solid #ff5252}
                            .btn-danger:hover{background:linear-gradient(135deg,#ff5252 0%,#e53e3e 100%);box-shadow:0 4px 15px rgba(255,107,107,0.4)}
                            .btn-success{background:linear-gradient(135deg,#4CAF50 0%,#45a049 100%);border:1px solid #4CAF50}
                            .btn-success:hover{background:linear-gradient(135deg,#45a049 0%,#3d8b40 100%);box-shadow:0 4px 15px rgba(76,175,80,0.4)}
                            .btn-warning{background:linear-gradient(135deg,#ff9800 0%,#f57c00 100%);border:1px solid #ff9800}
                            .btn-warning:hover{background:linear-gradient(135deg,#f57c00 0%,#ef6c00 100%);box-shadow:0 4px 15px rgba(255,152,0,0.4)}
                            .btn-secondary{background:linear-gradient(135deg,#6c757d 0%,#5a6268 100%);border:1px solid #6c757d}
                            .btn-secondary:hover{background:linear-gradient(135deg,#5a6268 0%,#495057 100%);box-shadow:0 4px 15px rgba(108,117,125,0.4)}
                            .btn-validate{grid-column:1 / -1;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%)}
                            .btn-resume{grid-column:1 / -1;background:#ef4444;margin-top:10px}
                            .btn:disabled{opacity:.5;cursor:not-allowed}
                            .toggle-item{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px}
                            .toggle-item:last-child{margin-bottom:0}
                            .toggle-label{font-size:13px;color:#ccc}
                            .toggle-switch{position:relative;width:40px;height:22px;background-color:#374151;border-radius:11px;cursor:pointer;transition:background .2s}
                            .toggle-switch.active{background-color:#667eea}
                            .toggle-switch::after{content:'';position:absolute;top:2px;left:2px;width:18px;height:18px;background-color:#fff;border-radius:50%;transition:transform .2s}
                            .toggle-switch.active::after{transform:translateX(18px)}
                            #session-selector{width:100%;padding:8px;background-color:#374151;border:none;border-radius:4px;color:#fff;margin-bottom:10px;display:none}
                            .setting-input-item{display:flex;justify-content:space-between;align-items:center}
                            .setting-input-item input{width:60px;background-color:#374151;border:1px solid #4a5568;color:#fff;border-radius:4px;padding:4px;text-align:center}
                            .completion-notice{position:absolute;top:0;right:60px;background:linear-gradient(135deg,#10b981 0%,#059669 100%);color:#fff;padding:12px 20px;border-radius:8px;box-shadow:0 4px 15px rgba(0,0,0,.2);font-size:15px;font-weight:500;animation:fadeInOut 4s ease-in-out forwards;pointer-events:none}
                            .stall-notice{background:#fbbf24;color:#1a1a1a;padding:10px;border-radius:6px;margin-bottom:10px;font-size:12px}
                            .storage-warning{background:#ef4444;color:#fff;padding:8px;border-radius:4px;margin-bottom:10px;font-size:12px}
                            @keyframes fadeInOut{0%,100%{opacity:0;transform:translateY(-20px)}10%,90%{opacity:1;transform:translateY(0)}}
                        </style>
                        <div class="aria-icon" id="aria-icon">
                            <svg viewBox="0 0 24 24"><path d="M19,9L20.25,6.25L23,5L20.25,3.75L19,1L17.75,3.75L15,5L17.75,6.25L19,9M19,15L17.75,17.75L15,19L17.75,20.25L19,23L20.25,20.25L23,19L20.25,17.75L19,15M11.5,9.5L9.5,4L7.5,9.5L2,11.5L7.5,13.5L9.5,19L11.5,13.5L17,11.5L11.5,9.5Z"/></svg>
                        </div>
                        <div class="aria-panel" id="aria-panel">
                            <div class="panel-header">
                                <h2 class="panel-title">Aria Downloader</h2>
                                <span class="panel-version">v3.9.0</span>
                            </div>
                            <select id="session-selector"></select>
                            <div id="stall-notice-container"></div>
                            <div id="storage-warning-container"></div>
                            <div class="status-section">
                                <div class="status-item">
                                    <span class="status-label">状态:</span>
                                    <span class="status-value" id="status">等待捕获...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">当前倍速:</span>
                                    <span class="status-value" id="current-speed">1.00x</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">视频大小:</span>
                                    <span class="status-value" id="video-size">0 MB</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">音频大小:</span>
                                    <span class="status-value" id="audio-size">0 MB</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">捕获时长:</span>
                                    <span class="status-value" id="duration">0:00</span>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-primary" id="dl-video" disabled>下载视频</button>
                                <button class="btn btn-primary" id="dl-audio" disabled>下载音频</button>
                                <button class="btn btn-success" id="resume-capture">智能续传</button>
                                <button class="btn btn-danger" id="clean-database">清理数据库</button>
                                <button class="btn btn-secondary" id="advanced-settings">高级设置</button>
                            </div>
                            <button class="btn btn-validate" id="dl-validate" disabled>全部下载</button>
                            <div class="settings-section" style="margin-top: 15px;">
                                <div class="toggle-item">
                                    <span class="toggle-label">启用智能无级变速</span>
                                    <div class="toggle-switch" data-setting="enableSpeedControl"></div>
                                </div>
                                <div class="toggle-item setting-input-item">
                                    <span class="toggle-label">最高变速倍率</span>
                                    <input type="number" id="max-speed-input" min="1" max="16" step="0.5">
                                </div>
                                <div class="toggle-item">
                                    <span class="toggle-label">捕获完成后自动下载</span>
                                    <div class="toggle-switch" data-setting="autoDownload"></div>
                                </div>
                                <div class="toggle-item">
                                    <span class="toggle-label">调试模式</span>
                                    <div class="toggle-switch" data-setting="debugMode"></div>
                                </div>
                            </div>
                        </div>
                    `;

          // 缓存元素引用
          this.cacheElements();

          // 绑定事件
          this.bindEvents();

          // 加载设置
          this.loadSettings();

          // 启动更新循环
          this.startUpdateLoop();

          // 检查存储空间
          this.checkStorageSpace();

          this.initialized = true;
          AriaController.logger.info("GUI initialized successfully");
        } catch (e) {
          AriaController.logger.error("Failed to create GUI:", e);
        }
      }

      cacheElements() {
        this.elements = {
          icon: this.shadowRoot.getElementById("aria-icon"),
          panel: this.shadowRoot.getElementById("aria-panel"),
          sessionSelector: this.shadowRoot.getElementById("session-selector"),
          status: this.shadowRoot.getElementById("status"),
          currentSpeed: this.shadowRoot.getElementById("current-speed"),
          videoSize: this.shadowRoot.getElementById("video-size"),
          audioSize: this.shadowRoot.getElementById("audio-size"),
          duration: this.shadowRoot.getElementById("duration"),
          dlVideo: this.shadowRoot.getElementById("dl-video"),
          dlAudio: this.shadowRoot.getElementById("dl-audio"),
          dlValidate: this.shadowRoot.getElementById("dl-validate"),
          resumeCapture: this.shadowRoot.getElementById("resume-capture"),
          cleanDatabase: this.shadowRoot.getElementById("clean-database"),
          advancedSettings: this.shadowRoot.getElementById("advanced-settings"),
          maxSpeedInput: this.shadowRoot.getElementById("max-speed-input"),
          stallNoticeContainer: this.shadowRoot.getElementById(
            "stall-notice-container",
          ),
          storageWarningContainer: this.shadowRoot.getElementById(
            "storage-warning-container",
          ),
          toggles: {
            enableSpeedControl: this.shadowRoot.querySelector(
              '[data-setting="enableSpeedControl"]',
            ),
            autoDownload: this.shadowRoot.querySelector(
              '[data-setting="autoDownload"]',
            ),
            debugMode: this.shadowRoot.querySelector(
              '[data-setting="debugMode"]',
            ),
          },
        };
      }

      bindEvents() {
        // 主图标点击
        this.elements.icon.addEventListener("click", (e) => {
          e.stopPropagation();
          this.elements.panel.classList.toggle("show");
        });

        // 点击外部关闭面板
        document.addEventListener("click", (e) => {
          if (!this.shadowHost.contains(e.target)) {
            this.elements.panel.classList.remove("show");
          }
        });

        // 会话选择
        this.elements.sessionSelector.addEventListener("change", (e) =>
          this.selectSession(e.target.value),
        );

        // 下载按钮（GUI负责检查）
        this.elements.dlVideo.addEventListener("click", () => {
          const session = this.controller.sessions.get(this.currentSessionId);
          if (!session) {
            alert("会话不存在");
            return;
          }
          if (!session.preparedBlobs.video) {
            alert("视频文件尚未准备就绪，请稍候...");
            return;
          }
          this.controller.downloadSession(this.currentSessionId, "video");
        });

        this.elements.dlAudio.addEventListener("click", () => {
          const session = this.controller.sessions.get(this.currentSessionId);
          if (!session) {
            alert("会话不存在");
            return;
          }
          if (!session.preparedBlobs.audio) {
            alert("音频文件尚未准备就绪，请稍候...");
            return;
          }
          this.controller.downloadSession(this.currentSessionId, "audio");
        });

        this.elements.dlValidate.addEventListener("click", () => {
          const session = this.controller.sessions.get(this.currentSessionId);
          if (!session) {
            alert("会话不存在");
            return;
          }
          if (!session.preparedBlobs.video && !session.preparedBlobs.audio) {
            alert("文件尚未准备就绪，请稍候...");
            return;
          }
          this.controller.downloadSession(this.currentSessionId, "both");
        });

        // v3.7.0 新增：断点续传按钮
        this.elements.resumeCapture.addEventListener("click", () => {
          this.handleResumeCapture();
        });

        // v3.6.1 新增：清理数据库按钮
        this.elements.cleanDatabase.addEventListener("click", () => {
          this.showCleanDatabaseDialog();
        });

        // v3.8.0 新增：高级设置按钮
        this.elements.advancedSettings.addEventListener("click", () => {
          this.showAdvancedSettingsDialog();
        });

        // 设置开关
        Object.entries(this.elements.toggles).forEach(([key, el]) => {
          el.addEventListener("click", () => {
            const isActive = el.classList.toggle("active");
            this.controller.updateSetting(key, isActive);
          });
        });

        // 速度输入
        this.elements.maxSpeedInput.addEventListener("change", (e) => {
          const value = parseFloat(e.target.value);
          if (value >= 1 && value <= 16) {
            this.controller.updateSetting("maxSpeed", value);
          }
        });
      }

      loadSettings() {
        const settings = this.controller.settings;

        for (const key in this.elements.toggles) {
          if (settings[key]) {
            this.elements.toggles[key].classList.add("active");
          }
        }

        this.elements.maxSpeedInput.value = settings.maxSpeed;
      }

      async checkStorageSpace() {
        const storage = await this.controller.dbManager.checkStorageUsage();

        if (storage && storage.percent > 90) {
          this.elements.storageWarningContainer.innerHTML = `
                        <div class="storage-warning">
                            存储空间不足！已使用 ${storage.percent.toFixed(1)}%
                        </div>
                    `;
        }
      }

      updateSessionList() {
        const sessions = Array.from(this.controller.sessions.values());

        if (sessions.length > 1) {
          this.elements.sessionSelector.style.display = "block";
          const currentId = this.currentSessionId;

          this.elements.sessionSelector.innerHTML = sessions
            .map(
              (s, i) =>
                `<option value="${s.id}">会话 ${i + 1} ${s.state === "stalled" ? "(已暂停)" : ""}</option>`,
            )
            .join("");

          if (currentId) {
            this.elements.sessionSelector.value = currentId;
          }
        } else {
          this.elements.sessionSelector.style.display = "none";
        }
      }

      selectSession(sessionId) {
        if (!sessionId || !this.controller.sessions.has(sessionId)) return;

        this.currentSessionId = sessionId;
        this.updateStatus();

        // 清理旧的提示
        this.elements.stallNoticeContainer.innerHTML = "";
        this.elements.storageWarningContainer.innerHTML = "";
      }

      updateStatus() {
        const session = this.controller.sessions.get(this.currentSessionId);
        if (!session) return;

        const statusMap = {
          capturing: "正在捕获...",
          complete: "捕获完成",
          error: "错误",
          stalled: "已暂停",
        };

        this.elements.status.textContent = statusMap[session.state] || "未知";
        this.elements.status.className = `status-value ${session.state}`;

        if (session.videoElement && !session.videoElement.paused) {
          this.elements.currentSpeed.textContent = `${session.videoElement.playbackRate.toFixed(2)}x`;
        } else {
          this.elements.currentSpeed.textContent = "暂停";
        }

        this.elements.videoSize.textContent = `${(session.capturedSize.video / 1024 / 1024).toFixed(2)} MB`;
        this.elements.audioSize.textContent = `${(session.capturedSize.audio / 1024 / 1024).toFixed(2)} MB`;

        const duration = (Date.now() - session.startTime) / 1000;
        this.elements.duration.textContent = this.formatDuration(duration);

        // 更新按钮状态（基于preparedBlobs）
        const hasVideoData = session.capturedSize.video > 0;
        const hasAudioData = session.capturedSize.audio > 0;
        const videoBlobReady = !!session.preparedBlobs.video;
        const audioBlobReady = !!session.preparedBlobs.audio;
        const canDownload = ["complete", "stalled", "error"].includes(
          session.state,
        );

        this.elements.dlVideo.disabled =
          !hasVideoData || !canDownload || !videoBlobReady;
        this.elements.dlAudio.disabled =
          !hasAudioData || !canDownload || !audioBlobReady;
        this.elements.dlValidate.disabled =
          (!hasVideoData && !hasAudioData) ||
          !canDownload ||
          (!videoBlobReady && !audioBlobReady);

        // 更新按钮文字提示准备状态
        if (hasVideoData && canDownload && !videoBlobReady) {
          this.elements.dlVideo.textContent = "准备中...";
        } else {
          this.elements.dlVideo.textContent = "下载视频";
        }

        if (hasAudioData && canDownload && !audioBlobReady) {
          this.elements.dlAudio.textContent = "准备中...";
        } else {
          this.elements.dlAudio.textContent = "下载音频";
        }
      }

      formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
          return `${hours}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
        } else {
          return `${minutes}:${secs.toString().padStart(2, "0")}`;
        }
      }

      startUpdateLoop() {
        if (this.updateInterval) return;

        this.updateInterval = setInterval(() => {
          if (this.currentSessionId) {
            this.updateStatus();
          }
        }, 1000);
      }

      showCompletionNotice() {
        let notice = this.shadowRoot.querySelector(".completion-notice");
        if (notice) notice.remove();

        notice = document.createElement("div");
        notice.className = "completion-notice";
        notice.textContent = "捕获完成，视频已暂停！";
        this.shadowRoot.appendChild(notice);

        setTimeout(() => notice.remove(), 4000);
      }

      showStalledState(sessionId) {
        if (sessionId !== this.currentSessionId) return;

        this.elements.stallNoticeContainer.innerHTML = `
                    <div class="stall-notice">
                        检测到缓冲停滞，捕获已暂停。点击下方按钮刷新页面并继续捕获。
                    </div>
                    <button class="btn btn-resume" id="btn-resume">断点续传</button>
                `;

        const resumeBtn = this.shadowRoot.getElementById("btn-resume");
        resumeBtn.addEventListener("click", () => {
          // 只负责转发事件给控制器
          this.controller.resumeSession(sessionId);
        });

        // 展开面板
        this.elements.panel.classList.add("show");
      }

      showResumePrompt() {
        if (!this.elements.panel.classList.contains("show")) {
          this.elements.panel.classList.add("show");
        }

        // 显示提示让用户手动点击播放
        const notice = document.createElement("div");
        notice.className = "stall-notice";
        notice.textContent = "请点击视频播放按钮继续捕获";
        notice.style.background = "#60a5fa";
        notice.style.color = "#fff";

        this.elements.stallNoticeContainer.appendChild(notice);

        setTimeout(() => notice.remove(), 5000);
      }

      // v3.6.1 新增：显示清理数据库对话框
      async showCleanDatabaseDialog() {
        try {
          // 获取存储使用情况
          const storageInfo = await this.controller.getStorageInfo();
          const currentSessionId = this.currentSessionId;

          const dialog = document.createElement("div");
          dialog.className = "clean-dialog";
          dialog.innerHTML = `
                        <h3>🗑️ 数据库清理</h3>
                        <p>此操作将清理除当前会话外的所有历史数据，释放存储空间。</p>

                        <div class="info-box">
                            <div class="storage-info">
                                <span class="label">总存储使用:</span>
                                <span class="value">${(storageInfo.totalSize / 1024 / 1024).toFixed(2)} MB</span>
                            </div>
                            <div class="storage-info">
                                <span class="label">历史会话数:</span>
                                <span class="value">${storageInfo.sessionCount}</span>
                            </div>
                            <div class="storage-info">
                                <span class="label">当前会话:</span>
                                <span class="value">${currentSessionId ? currentSessionId.slice(-8) : "无"}</span>
                            </div>
                        </div>

                        <p class="warning">⚠️ 警告：此操作不可撤销！清理后的数据无法恢复。</p>

                        <div class="buttons">
                            <button class="dialog-btn cancel-btn">取消</button>
                            <button class="dialog-btn confirm-btn">确认清理</button>
                        </div>
                    `;

          // 添加样式
          const style = document.createElement("style");
          style.textContent = `
                        .clean-dialog {
                            position: fixed;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            background: #2d2d2d;
                            border: 2px solid #4CAF50;
                            border-radius: 12px;
                            padding: 25px;
                            z-index: 10001;
                            min-width: 450px;
                            max-width: 600px;
                            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        }
                        .clean-dialog h3 {
                            color: #4CAF50;
                            margin: 0 0 20px 0;
                            font-size: 20px;
                            text-align: center;
                        }
                        .clean-dialog p {
                            color: #fff;
                            margin: 12px 0;
                            line-height: 1.5;
                        }
                        .clean-dialog .warning {
                            color: #ff6b6b;
                            font-weight: bold;
                            background: rgba(255, 107, 107, 0.1);
                            padding: 10px;
                            border-radius: 6px;
                            border-left: 4px solid #ff6b6b;
                        }
                        .clean-dialog .info-box {
                            background: #333;
                            padding: 15px;
                            border-radius: 6px;
                            margin: 15px 0;
                            font-family: 'Courier New', monospace;
                            font-size: 13px;
                            border-left: 4px solid #4CAF50;
                        }
                        .clean-dialog .storage-info {
                            display: flex;
                            justify-content: space-between;
                            margin: 8px 0;
                        }
                        .clean-dialog .storage-info .label {
                            color: #aaa;
                        }
                        .clean-dialog .storage-info .value {
                            color: #4CAF50;
                            font-weight: bold;
                        }
                        .clean-dialog .buttons {
                            display: flex;
                            gap: 12px;
                            justify-content: center;
                            margin-top: 25px;
                        }
                        .clean-dialog .dialog-btn {
                            padding: 10px 20px;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 14px;
                            transition: all 0.3s ease;
                        }
                        .clean-dialog .confirm-btn {
                            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                            color: white;
                        }
                        .clean-dialog .confirm-btn:hover {
                            background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%);
                            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
                        }
                        .clean-dialog .cancel-btn {
                            background: linear-gradient(135deg, #666 0%, #555 100%);
                            color: white;
                        }
                        .clean-dialog .cancel-btn:hover {
                            background: linear-gradient(135deg, #777 0%, #666 100%);
                        }
                    `;

          document.head.appendChild(style);
          document.body.appendChild(dialog);

          // 事件处理
          const cancelBtn = dialog.querySelector(".cancel-btn");
          const confirmBtn = dialog.querySelector(".confirm-btn");

          const cleanup = () => {
            document.body.removeChild(dialog);
            document.head.removeChild(style);
          };

          cancelBtn.addEventListener("click", cleanup);

          confirmBtn.addEventListener("click", async () => {
            try {
              confirmBtn.textContent = "清理中...";
              confirmBtn.disabled = true;

              await this.controller.cleanDatabaseExceptCurrent();

              cleanup();
              this.controller.logger.info("Database cleaned successfully");

              // 显示成功提示
              this.showNotification("数据库清理完成！", "success");
            } catch (error) {
              this.controller.logger.error("Database clean failed:", error);
              this.showNotification("清理失败：" + error.message, "error");
              cleanup();
            }
          });
        } catch (error) {
          this.controller.logger.error("Failed to show clean dialog:", error);
          this.showNotification("无法显示清理对话框", "error");
        }
      }

      // v3.6.1 新增：显示通知
      showNotification(message, type = "info") {
        const notification = document.createElement("div");
        const bgColor =
          type === "success"
            ? "#4CAF50"
            : type === "error"
              ? "#ff6b6b"
              : type === "warning"
                ? "#ff9800"
                : "#2196F3";

        notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 6px;
                    color: white;
                    font-weight: bold;
                    z-index: 10002;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    background: ${bgColor};
                    max-width: 300px;
                    word-wrap: break-word;
                `;
        notification.textContent = message;

        document.body.appendChild(notification);

        const duration = type === "warning" ? 5000 : 3000; // 警告显示更久
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, duration);
      }

      // v3.6.2 紧急修复：显示紧急清理通知
      showEmergencyCleanupNotice() {
        const notice = document.createElement("div");
        notice.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: #1a1a1a;
                    border: 3px solid #ff6b6b;
                    border-radius: 12px;
                    padding: 30px;
                    z-index: 10003;
                    min-width: 400px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.8);
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    text-align: center;
                `;

        notice.innerHTML = `
                    <h2 style="color: #ff6b6b; margin: 0 0 20px 0; font-size: 24px;">
                        🚨 紧急清理完成
                    </h2>
                    <p style="color: #fff; margin: 15px 0; line-height: 1.5;">
                        由于存储空间严重不足，系统已自动清理所有历史数据。
                    </p>
                    <p style="color: #4CAF50; margin: 15px 0; font-weight: bold;">
                        ✅ 存储空间已释放，可以重新开始捕获
                    </p>
                    <button style="
                        background: #4CAF50;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 6px;
                        font-weight: bold;
                        cursor: pointer;
                        margin-top: 20px;
                    " onclick="this.parentElement.remove(); location.reload();">
                        刷新页面
                    </button>
                `;

        document.body.appendChild(notice);

        // 10秒后自动刷新
        setTimeout(() => {
          location.reload();
        }, 10000);
      }

      // v3.8.0 新增：显示高级设置对话框
      showAdvancedSettingsDialog() {
        const dialog = document.createElement("div");
        dialog.className = "clean-dialog";
        dialog.innerHTML = `
                    <h3>⚙️ 高级设置</h3>
                    <p>配置存储阈值和会话管理选项</p>

                    <div class="settings-section">
                        <h4 style="color: #4CAF50; margin: 15px 0 10px 0;">存储管理设置</h4>

                        <div class="setting-item" style="margin: 10px 0;">
                            <label style="color: #fff; display: block; margin-bottom: 5px;">
                                存储警告阈值 (当前: ${(ARIA_USER_CONFIG.STORAGE_WARNING_THRESHOLD * 100).toFixed(0)}%)
                            </label>
                            <input type="range" id="warning-threshold" min="70" max="95"
                                   value="${ARIA_USER_CONFIG.STORAGE_WARNING_THRESHOLD * 100}"
                                   style="width: 100%; margin-bottom: 5px;">
                            <span id="warning-value" style="color: #aaa; font-size: 12px;">
                                ${(ARIA_USER_CONFIG.STORAGE_WARNING_THRESHOLD * 100).toFixed(0)}%
                            </span>
                        </div>

                        <div class="setting-item" style="margin: 10px 0;">
                            <label style="color: #fff; display: block; margin-bottom: 5px;">
                                存储关键阈值 (当前: ${(ARIA_USER_CONFIG.STORAGE_CRITICAL_THRESHOLD * 100).toFixed(0)}%)
                            </label>
                            <input type="range" id="critical-threshold" min="85" max="99"
                                   value="${ARIA_USER_CONFIG.STORAGE_CRITICAL_THRESHOLD * 100}"
                                   style="width: 100%; margin-bottom: 5px;">
                            <span id="critical-value" style="color: #aaa; font-size: 12px;">
                                ${(ARIA_USER_CONFIG.STORAGE_CRITICAL_THRESHOLD * 100).toFixed(0)}%
                            </span>
                        </div>

                        <h4 style="color: #4CAF50; margin: 20px 0 10px 0;">会话管理器</h4>

                        <div class="session-manager" style="background: #333; padding: 15px; border-radius: 6px; margin: 10px 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="color: #fff; font-weight: bold;">历史会话 (点击删除单个会话)</span>
                                <button class="dialog-btn" id="refresh-sessions" style="background: #2196F3; padding: 5px 10px; font-size: 12px;">
                                    刷新
                                </button>
                            </div>
                            <div id="session-list" style="max-height: 200px; overflow-y: auto;">
                                <div style="color: #aaa; text-align: center; padding: 20px;">加载中...</div>
                            </div>
                        </div>
                    </div>

                    <div class="buttons">
                        <button class="dialog-btn cancel-btn">取消</button>
                        <button class="dialog-btn confirm-btn">保存设置</button>
                    </div>
                `;

        document.body.appendChild(dialog);

        // 设置滑块事件
        const warningSlider = dialog.querySelector("#warning-threshold");
        const criticalSlider = dialog.querySelector("#critical-threshold");
        const warningValue = dialog.querySelector("#warning-value");
        const criticalValue = dialog.querySelector("#critical-value");

        warningSlider.addEventListener("input", (e) => {
          warningValue.textContent = e.target.value + "%";
        });

        criticalSlider.addEventListener("input", (e) => {
          criticalValue.textContent = e.target.value + "%";
        });

        // 加载会话列表
        this.loadSessionList(dialog.querySelector("#session-list"));

        // 刷新按钮事件
        dialog
          .querySelector("#refresh-sessions")
          .addEventListener("click", () => {
            this.loadSessionList(dialog.querySelector("#session-list"));
          });

        // 按钮事件
        const cancelBtn = dialog.querySelector(".cancel-btn");
        const confirmBtn = dialog.querySelector(".confirm-btn");

        const cleanup = () => {
          document.body.removeChild(dialog);
        };

        cancelBtn.addEventListener("click", cleanup);

        confirmBtn.addEventListener("click", () => {
          try {
            // 保存设置
            const newWarningThreshold = parseInt(warningSlider.value) / 100;
            const newCriticalThreshold = parseInt(criticalSlider.value) / 100;

            // 验证设置合理性
            if (newWarningThreshold >= newCriticalThreshold) {
              this.showNotification("警告阈值必须小于关键阈值", "error");
              return;
            }

            // 更新配置
            ARIA_USER_CONFIG.STORAGE_WARNING_THRESHOLD = newWarningThreshold;
            ARIA_USER_CONFIG.STORAGE_CRITICAL_THRESHOLD = newCriticalThreshold;

            cleanup();
            this.showNotification("设置已保存", "success");
          } catch (error) {
            this.showNotification("保存失败：" + error.message, "error");
          }
        });
      }

      // v3.8.0 新增：加载会话列表
      async loadSessionList(container) {
        try {
          container.innerHTML =
            '<div style="color: #aaa; text-align: center; padding: 10px;">加载中...</div>';

          const allSessions =
            this.controller.heartbeatManager.getAllStoredSessions();
          const sessionEntries = Object.entries(allSessions);

          if (sessionEntries.length === 0) {
            container.innerHTML =
              '<div style="color: #aaa; text-align: center; padding: 10px;">没有历史会话</div>';
            return;
          }

          const sessionItems = sessionEntries.map(
            ([sessionId, sessionData]) => {
              const videoId =
                this.controller.extractVideoId(sessionData.url) || "未识别";
              const totalSize =
                (sessionData.capturedSize?.video || 0) +
                (sessionData.capturedSize?.audio || 0);
              const sizeText = (totalSize / 1024 / 1024).toFixed(1) + "MB";
              const timeText = new Date(
                sessionData.timestamp,
              ).toLocaleDateString();

              return `
                            <div class="session-item" style="
                                background: #444;
                                padding: 8px;
                                margin: 3px 0;
                                border-radius: 4px;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                font-size: 11px;
                            ">
                                <div style="flex: 1;">
                                    <div style="color: #4CAF50; font-weight: bold;">
                                        ${sessionId.slice(-8)} (${videoId})
                                    </div>
                                    <div style="color: #aaa;">
                                        ${timeText} - ${sizeText}
                                    </div>
                                </div>
                                <button class="delete-session" data-session-id="${sessionId}" style="
                                    background: #ff6b6b;
                                    border: none;
                                    color: white;
                                    padding: 3px 6px;
                                    border-radius: 3px;
                                    font-size: 10px;
                                    cursor: pointer;
                                ">删除</button>
                            </div>
                        `;
            },
          );

          container.innerHTML = sessionItems.join("");

          // 添加删除按钮事件
          container.querySelectorAll(".delete-session").forEach((btn) => {
            btn.addEventListener("click", async (e) => {
              const sessionId = e.target.dataset.sessionId;
              if (confirm(`确定要删除会话 ${sessionId.slice(-8)} 吗？`)) {
                try {
                  // 删除localStorage中的会话
                  const sessions =
                    this.controller.heartbeatManager.getAllStoredSessions();
                  delete sessions[sessionId];
                  localStorage.setItem(
                    "aria_active_sessions",
                    JSON.stringify(sessions),
                  );

                  // 删除IndexedDB中的数据
                  await this.controller.dbManager.deleteSession(sessionId);

                  this.showNotification("会话已删除", "success");
                  this.loadSessionList(container); // 重新加载列表
                } catch (error) {
                  this.showNotification("删除失败：" + error.message, "error");
                }
              }
            });
          });
        } catch (error) {
          container.innerHTML = `<div style="color: #ff6b6b; text-align: center; padding: 10px;">加载失败: ${error.message}</div>`;
        }
      }

      // v3.8.0 革命性改进：常驻续传按钮管理
      notifyResumableSessions(resumableSessions) {
        this.resumableSessions = resumableSessions;

        // v3.8.0 核心：按钮始终显示，但状态不同
        this.elements.resumeCapture.style.display = "block";
        this.elements.resumeCapture.disabled = false;
        this.elements.resumeCapture.textContent = `智能续传 (${resumableSessions.length})`;
        this.elements.resumeCapture.className = "btn btn-success"; // 绿色表示有可续传会话

        // 更新状态显示
        const totalSize = resumableSessions.reduce(
          (sum, session) =>
            sum +
            (session.estimatedSize.video || 0) +
            (session.estimatedSize.audio || 0),
          0,
        );

        // 显示续传提示通知
        this.showNotification(
          `发现 ${resumableSessions.length} 个可续传会话，总计 ${(totalSize / 1024 / 1024).toFixed(1)}MB`,
          "info",
        );

        this.controller.logger.info("Resume sessions notified to GUI");
      }

      // v3.8.0 新增：通知没有可续传会话
      notifyNoResumableSessions() {
        this.resumableSessions = [];

        // v3.8.0 核心：按钮始终显示，但状态为强制模式
        this.elements.resumeCapture.style.display = "block";
        this.elements.resumeCapture.disabled = false;
        this.elements.resumeCapture.textContent = "强制续传";
        this.elements.resumeCapture.className = "btn btn-warning"; // 黄色表示强制模式

        this.controller.logger.info(
          "No resumable sessions, button in force mode",
        );
      }

      // v3.8.0 革命性改进：智能续传处理
      handleResumeCapture() {
        if (!this.resumableSessions || this.resumableSessions.length === 0) {
          // v3.8.0 新增：强制续传模式
          this.showForceResumeDialog();
          return;
        }

        if (this.resumableSessions.length === 1) {
          // 只有一个会话，直接续传
          this.resumeSession(this.resumableSessions[0]);
        } else {
          // 多个会话，显示选择对话框
          this.showResumeSelectionDialog(this.resumableSessions);
        }
      }

      // v3.8.0 新增：强制续传对话框
      showForceResumeDialog() {
        const dialog = document.createElement("div");
        dialog.className = "clean-dialog";
        dialog.innerHTML = `
                    <h3>⚡ 强制续传模式</h3>
                    <p>当前没有检测到可续传的会话，但您可以尝试强制续传操作：</p>

                    <div class="info-box">
                        <div class="storage-info">
                            <span class="label">当前视频ID:</span>
                            <span class="value">${this.controller.extractVideoId(window.location.href) || "未识别"}</span>
                        </div>
                        <div class="storage-info">
                            <span class="label">存储的会话:</span>
                            <span class="value" id="stored-sessions-count">检查中...</span>
                        </div>
                    </div>

                    <div class="buttons">
                        <button class="dialog-btn cancel-btn">取消</button>
                        <button class="dialog-btn confirm-btn">扫描所有会话</button>
                        <button class="dialog-btn" style="background: #ff9800;">强制锁定当前</button>
                    </div>
                `;

        document.body.appendChild(dialog);

        // 异步获取存储会话数量
        setTimeout(async () => {
          try {
            const allSessions =
              this.controller.heartbeatManager.getAllStoredSessions();
            const sessionCount = Object.keys(allSessions).length;
            dialog.querySelector("#stored-sessions-count").textContent =
              `${sessionCount} 个`;
          } catch (e) {
            dialog.querySelector("#stored-sessions-count").textContent =
              "获取失败";
          }
        }, 100);

        const cancelBtn = dialog.querySelector(".cancel-btn");
        const scanBtn = dialog.querySelector(".confirm-btn");
        const forceBtn = dialog.querySelectorAll(".dialog-btn")[2];

        const cleanup = () => {
          document.body.removeChild(dialog);
        };

        cancelBtn.addEventListener("click", cleanup);

        scanBtn.addEventListener("click", async () => {
          try {
            scanBtn.textContent = "扫描中...";
            scanBtn.disabled = true;

            // 强制重新扫描所有会话
            const allSessions = await this.controller.forceRescanSessions();

            cleanup();

            if (allSessions.length > 0) {
              this.showResumeSelectionDialog(allSessions);
            } else {
              this.showNotification("没有找到任何可续传的会话", "warning");
            }
          } catch (error) {
            this.showNotification("扫描失败：" + error.message, "error");
            cleanup();
          }
        });

        forceBtn.addEventListener("click", async () => {
          try {
            forceBtn.textContent = "锁定中...";
            forceBtn.disabled = true;

            // 强制锁定当前页面为续传目标
            await this.controller.forceLockCurrentPage();

            cleanup();
            this.showNotification(
              "已锁定当前页面，刷新后将自动续传",
              "success",
            );
          } catch (error) {
            this.showNotification("锁定失败：" + error.message, "error");
            cleanup();
          }
        });
      }

      // v3.7.0 新增：续传指定会话
      async resumeSession(resumeInfo) {
        try {
          this.showNotification("正在恢复会话...", "info");

          // 调用控制器的续传方法
          await this.controller.resumeSessionEnhanced(
            resumeInfo.sessionId,
            resumeInfo.resumeLevel,
          );

          // 隐藏续传按钮
          this.elements.resumeCapture.style.display = "none";
          this.resumableSessions = [];

          this.showNotification("会话恢复成功！可以继续捕获", "success");
        } catch (error) {
          this.controller.logger.error("Resume session failed:", error);
          this.showNotification("恢复失败：" + error.message, "error");
        }
      }

      // v3.7.0 新增：显示续传选择对话框
      showResumeSelectionDialog(resumableSessions) {
        const dialog = document.createElement("div");
        dialog.className = "clean-dialog"; // 复用样式
        dialog.innerHTML = `
                    <h3>🔄 选择要续传的会话</h3>
                    <p>发现 ${resumableSessions.length} 个可续传的会话，请选择要恢复的会话：</p>

                    <div class="resume-sessions-list">
                        ${resumableSessions
                          .map((session, index) => {
                            // v3.8.0 增强：提取并显示原始URL和视频ID
                            const originalUrl =
                              session.sessionData.url || "未知";
                            const videoId =
                              session.videoId ||
                              this.controller.extractVideoId(originalUrl) ||
                              "未识别";
                            const shortUrl =
                              originalUrl.length > 50
                                ? originalUrl.substring(0, 47) + "..."
                                : originalUrl;

                            // v3.8.0 增强：检查数据间隙并显示警告
                            const hasDataGaps =
                              session.dataGaps &&
                              (session.dataGaps.videoGaps?.length > 0 ||
                                session.dataGaps.audioGaps?.length > 0);
                            const gapWarning = hasDataGaps
                              ? "⚠️ 数据不连续，可能损坏"
                              : "";

                            return `
                            <div class="resume-session-item" data-session-id="${session.sessionId}" style="
                                background: #333;
                                padding: 15px;
                                margin: 10px 0;
                                border-radius: 6px;
                                border-left: 4px solid ${hasDataGaps ? "#ff9800" : "#4CAF50"};
                                cursor: pointer;
                                transition: background 0.3s ease;
                            " onmouseover="this.style.background='#444'" onmouseout="this.style.background='#333'">
                                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                                    <div style="flex: 1; margin-right: 15px;">
                                        <div style="color: #4CAF50; font-weight: bold; margin-bottom: 5px;">
                                            会话 ${session.sessionId.slice(-8)}
                                        </div>
                                        <div style="color: #aaa; font-size: 11px; margin-bottom: 3px;">
                                            时间: ${new Date(session.lastActiveTime).toLocaleString()}
                                        </div>
                                        <div style="color: #aaa; font-size: 11px; margin-bottom: 3px;">
                                            视频ID: ${videoId}
                                        </div>
                                        <div style="color: #aaa; font-size: 11px; word-break: break-all;">
                                            原始URL: ${shortUrl}
                                        </div>
                                        ${
                                          hasDataGaps
                                            ? `
                                        <div style="color: #ff9800; font-size: 11px; margin-top: 5px; font-weight: bold;">
                                            ${gapWarning}
                                        </div>
                                        `
                                            : ""
                                        }
                                    </div>
                                    <div style="text-align: right; min-width: 100px;">
                                        <div style="color: #fff; font-size: 12px;">
                                            视频: ${((session.estimatedSize.video || 0) / 1024 / 1024).toFixed(1)}MB
                                        </div>
                                        <div style="color: #fff; font-size: 12px;">
                                            音频: ${((session.estimatedSize.audio || 0) / 1024 / 1024).toFixed(1)}MB
                                        </div>
                                        <div style="color: ${session.dataIntegrity ? "#4CAF50" : "#ff6b6b"}; font-size: 11px; margin-top: 3px;">
                                            ${session.dataIntegrity ? "✅ 数据完整" : "⚠️ 可能损坏"}
                                        </div>
                                        ${
                                          hasDataGaps
                                            ? `
                                        <div style="color: #ff9800; font-size: 10px; margin-top: 2px;">
                                            发现数据间隙
                                        </div>
                                        `
                                            : ""
                                        }
                                    </div>
                                </div>
                            </div>
                            `;
                          })
                          .join("")}
                    </div>

                    <div class="buttons">
                        <button class="dialog-btn cancel-btn">取消</button>
                        <button class="dialog-btn confirm-btn" disabled>续传选中会话</button>
                    </div>
                `;

        document.body.appendChild(dialog);

        let selectedSession = null;
        const confirmBtn = dialog.querySelector(".confirm-btn");
        const cancelBtn = dialog.querySelector(".cancel-btn");

        // 会话选择事件
        dialog.querySelectorAll(".resume-session-item").forEach((item) => {
          item.addEventListener("click", () => {
            // 清除之前的选择
            dialog.querySelectorAll(".resume-session-item").forEach((i) => {
              i.style.borderLeft = "4px solid #4CAF50";
            });

            // 标记当前选择
            item.style.borderLeft = "4px solid #ff6b6b";

            // 启用确认按钮
            selectedSession = resumableSessions.find(
              (s) => s.sessionId === item.dataset.sessionId,
            );
            confirmBtn.disabled = false;
          });
        });

        // 按钮事件
        cancelBtn.addEventListener("click", () => {
          document.body.removeChild(dialog);
        });

        confirmBtn.addEventListener("click", () => {
          if (selectedSession) {
            document.body.removeChild(dialog);
            this.resumeSession(selectedSession);
          }
        });
      }
    }

    // 主控制器 - v3.2.0 时序优化版
    class AriaController {
      constructor() {
        this.logger = new Logger();
        this.dbManager = new DBManager();
        this.hijackManager = new HijackManager();
        this.heartbeatManager = new HeartbeatManager(); // 无状态的心跳管理器
        this.guiManager = new GUIManager(this);
        this.sessions = new Map(); // 唯一的状态源
        this.initialized = false;
        this.settings = {};
        this.cleanupInterval = null;
        this.heartbeatInterval = null;

        // v3.9.0 关键修复：初始化状态管理
        this.isInitializing = false;
        this.pendingSessions = [];
        this.blockNewSessions = false;
      }

      static getInstance() {
        if (!AriaController.instance) {
          AriaController.instance = new AriaController();
        }
        return AriaController.instance;
      }

      loadSettings() {
        this.settings = {
          enableSpeedControl: GM_getValue("enableSpeedControl", true),
          maxSpeed: GM_getValue("maxSpeed", ARIA_USER_CONFIG.DEFAULT_MAX_SPEED),
          autoDownload: GM_getValue("autoDownload", false),
          debugMode: GM_getValue("debugMode", false),
        };

        this.logger.setLevel(this.settings.debugMode ? "debug" : "info");
      }

      updateSetting(key, value) {
        this.settings[key] = value;
        GM_setValue(key, value);

        if (key === "debugMode") {
          this.logger.setLevel(value ? "debug" : "info");
        }

        // 实时更新所有会话的设置
        this.sessions.forEach((session) => {
          if (session.speedController) {
            if (key === "enableSpeedControl") {
              value
                ? session.speedController.start()
                : session.speedController.stop();
            }
            if (key === "maxSpeed") {
              session.speedController.updateMaxSpeed(value);
            }
          }
        });
      }

      async init() {
        if (this.initialized) return;

        try {
          // v3.9.0 关键修复：设置初始化状态锁
          this.isInitializing = true;
          this.logger.info("Initializing AriaController...");

          this.loadSettings();

          // 重要：先初始化UI
          this.guiManager.init();

          // 然后初始化数据库
          await this.dbManager.init();

          // v3.4.2 修复：移除启动时的激进清理，避免多标签页冲突
          // await this._performCleanup(); // 注释掉这行！

          // 劫持MediaSource
          this.hijackManager.init();

          // v3.4.3 增强：监控传统视频元素
          this.monitorTraditionalVideo();

          // v3.6.2 紧急修复：检查存储使用情况
          await this.checkStorageUsage();

          // v3.8.0 新增：检查强制锁定状态
          if (this.checkForceLock()) {
            this.logger.info("Force lock mode activated");
          }

          this.initialized = true;

          // v3.9.0 关键修复：清除初始化状态锁
          this.isInitializing = false;

          this.logger.info("AriaController initialized successfully");

          // v3.9.0 修复：处理初始化期间排队的会话
          if (this.pendingSessions && this.pendingSessions.length > 0) {
            this.logger.info(
              `Processing ${this.pendingSessions.length} pending sessions`,
            );
            const sessionsToProcess = [...this.pendingSessions];
            this.pendingSessions = [];

            sessionsToProcess.forEach((session, index) => {
              setTimeout(() => {
                this.logger.info(
                  `Processing queued session ${index + 1}/${sessionsToProcess.length}: ${session.id}`,
                );
                this.registerSession(session);
              }, index * 100);
            });
          }

          // 检查是否是恢复会话
          this.checkForResume();

          // 启动定期任务（延迟启动，避免冲突）
          setTimeout(() => {
            this.startPeriodicTasks();
          }, 30000); // 30秒后再启动定期任务
        } catch (error) {
          this.logger.error("Failed to initialize AriaController:", error);
          // v3.9.0 修复：确保在错误情况下也清除初始化锁
          this.isInitializing = false;
        }
      }

      startPeriodicTasks() {
        // 定期清理（每5分钟）
        this.cleanupInterval = setInterval(() => {
          this._performCleanup();
        }, ARIA_USER_CONFIG.CLEANUP_INTERVAL);

        // 统一的心跳更新（每5秒）
        this.heartbeatInterval = setInterval(() => {
          this.updateAllHeartbeats();
        }, ARIA_USER_CONFIG.HEARTBEAT_INTERVAL);

        // 页面卸载时停止
        window.addEventListener("beforeunload", () => {
          if (this.cleanupInterval) clearInterval(this.cleanupInterval);
          if (this.heartbeatInterval) clearInterval(this.heartbeatInterval);

          // 移除所有会话的心跳
          for (const session of this.sessions.values()) {
            this.heartbeatManager.removeSession(session.id);
          }
        });
      }

      updateAllHeartbeats() {
        // 遍历内存中的会话，更新心跳
        for (const session of this.sessions.values()) {
          if (session.state === "capturing" || session.state === "stalled") {
            this.heartbeatManager.updateSession(
              session.id,
              session.getHeartbeatData(),
            );
          }
        }
      }

      updateSessionHeartbeat(sessionId, sessionData) {
        // 单个会话的心跳更新
        this.heartbeatManager.updateSession(sessionId, sessionData);
      }

      // 统一的清理入口
      async _performCleanup() {
        this.logger.info("Performing cleanup...");

        try {
          // 1. 获取所有数据
          const dbSessionIds = await this.dbManager.getAllSessionIds();
          const activeSessions = this.heartbeatManager.getActiveSessions();
          const activeIds = Object.keys(activeSessions);
          const protectedId = new URLSearchParams(window.location.search).get(
            "aria_resume_id",
          );
          const memoryIds = Array.from(this.sessions.keys());

          // 2. 计算需要清理的孤儿会话
          const orphanedIds = dbSessionIds.filter(
            (id) =>
              !activeIds.includes(id) &&
              !memoryIds.includes(id) &&
              id !== protectedId,
          );

          // 3. 执行数据库清理
          for (const id of orphanedIds) {
            await this.dbManager.deleteSession(id);
            this.logger.debug(`Cleaned orphaned session from DB: ${id}`);
          }

          // 4. 清理内存中的过期会话
          const now = Date.now();
          const sessionsToRemove = [];

          for (const [id, session] of this.sessions) {
            if (session.state === "complete" || session.state === "error") {
              const elapsed = now - session.startTime;
              if (elapsed > ARIA_USER_CONFIG.SESSION_EXPIRE_TIME) {
                sessionsToRemove.push(id);
              }
            }
          }

          for (const id of sessionsToRemove) {
            const session = this.sessions.get(id);
            if (session) {
              session.cleanup();
              this.sessions.delete(id);
              this.heartbeatManager.removeSession(id);
              this.logger.debug(`Cleaned expired session from memory: ${id}`);
            }
          }

          // 5. 清理localStorage中的过期心跳
          const cleanedExpired = this.heartbeatManager.cleanupExpiredSessions();
          if (cleanedExpired) {
            this.logger.debug("Cleaned expired heartbeats from localStorage");
          }

          // 6. 更新UI
          if (sessionsToRemove.length > 0) {
            this.guiManager.updateSessionList();
          }

          this.logger.info(
            `Cleanup completed. Cleaned ${orphanedIds.length} orphaned DB sessions and ${sessionsToRemove.length} expired memory sessions`,
          );
        } catch (e) {
          this.logger.error("Cleanup failed:", e);
        }
      }

      requestCleanup() {
        // 可以被其他组件调用来请求立即清理
        this._performCleanup();
      }

      // v3.8.0 革命性改进：自动化断点续传检查
      checkForResume() {
        // 检查URL参数中的恢复信息
        const urlParams = new URLSearchParams(window.location.search);
        const resumeId = urlParams.get("aria_resume_id");
        const resumeTime = urlParams.get("aria_resume_time");

        if (resumeId && resumeTime) {
          this.logger.info(
            `URL Resume detected: session=${resumeId}, time=${resumeTime}`,
          );
          this.handleUrlResume(resumeId, resumeTime);
          return;
        }

        // v3.8.0 核心改进：自动化会话识别和处理
        const resumableSessions = this.identifyResumableSessions();

        if (resumableSessions.length > 0) {
          this.logger.info(
            `Found ${resumableSessions.length} resumable sessions`,
          );

          // 通知GUI有可续传会话
          this.guiManager.notifyResumableSessions(resumableSessions);

          // v3.8.0 新增：自动选择最佳会话进行续传
          if (resumableSessions.length === 1) {
            this.logger.info("Auto-resuming single session");
            setTimeout(() => {
              this.autoResumeSession(resumableSessions[0]);
            }, 1000); // 1秒后自动续传
          } else {
            this.logger.info(
              "Multiple sessions found, waiting for user selection",
            );
          }
        } else {
          this.logger.info("No resumable sessions found");
          // 通知GUI没有可续传会话
          this.guiManager.notifyNoResumableSessions();
        }
      }

      // v3.8.0 新增：自动续传会话
      async autoResumeSession(resumeInfo) {
        try {
          this.logger.info(`Auto-resuming session ${resumeInfo.sessionId}`);

          // 阻止新会话创建
          this.blockNewSessions = true;

          // 执行续传
          await this.resumeSessionEnhanced(
            resumeInfo.sessionId,
            resumeInfo.resumeLevel,
          );

          // 通知用户
          this.guiManager.showNotification("自动续传成功！", "success");
        } catch (error) {
          this.logger.error("Auto-resume failed:", error);
          this.guiManager.showNotification(
            "自动续传失败：" + error.message,
            "error",
          );
          this.blockNewSessions = false;
        }
      }

      // v3.6.0 新增：处理URL恢复
      handleUrlResume(resumeId, resumeTime) {
        // 使用MutationObserver等待视频元素
        const observer = new MutationObserver((mutations, obs) => {
          const videos = Array.from(document.querySelectorAll("video")).filter(
            (v) => v.src && v.src.startsWith("blob:"),
          );

          if (videos.length > 0) {
            obs.disconnect();

            const video = videos[0];

            // 等待视频metadata加载
            const performResume = () => {
              try {
                video.currentTime = parseFloat(resumeTime);

                // 尝试自动播放
                video
                  .play()
                  .then(() => {
                    this.logger.info("Resume playback successful");
                  })
                  .catch((e) => {
                    this.logger.error("Failed to resume playback:", e);

                    // 需要用户交互
                    if (e.name === "NotAllowedError") {
                      this.guiManager.showResumePrompt();
                    }
                  });
              } catch (e) {
                this.logger.error("Resume error:", e);
              }
            };

            if (video.readyState >= 1) {
              performResume();
            } else {
              video.addEventListener("loadedmetadata", performResume, {
                once: true,
              });
            }
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });

        // 超时保护
        setTimeout(() => {
          observer.disconnect();
          this.logger.warn("Resume timeout - no video element found");
        }, 10000);
      }

      // v3.7.1 修复：增强的会话识别逻辑
      identifyResumableSessions() {
        const currentVideoId = this.extractVideoId(window.location.href);
        if (!currentVideoId) return [];

        const storedSessions = this.heartbeatManager.getAllStoredSessions();
        const resumableSessions = [];

        for (const [sessionId, sessionData] of Object.entries(storedSessions)) {
          const sessionVideoId = this.extractVideoId(sessionData.url);

          // v3.7.1 修复：更严格的会话有效性检查
          if (
            sessionVideoId === currentVideoId &&
            this.isSessionResumable(sessionData) &&
            this.isSessionValid(sessionData)
          ) {
            const resumeInfo = {
              sessionId,
              sessionData,
              resumeLevel: this.determineResumeLevel(sessionData),
              dataIntegrity: this.checkDataIntegrity(sessionId),
              estimatedSize: sessionData.capturedSize || { video: 0, audio: 0 },
              lastActiveTime: sessionData.timestamp,
            };
            resumableSessions.push(resumeInfo);
          }
        }

        return resumableSessions.sort(
          (a, b) => b.lastActiveTime - a.lastActiveTime,
        );
      }

      // v3.7.1 新增：检查会话是否可续传
      isSessionResumable(sessionData) {
        // 检查状态是否允许续传
        const resumableStates = ["capturing", "completed", "paused"];
        if (!resumableStates.includes(sessionData.state)) {
          return false;
        }

        // 检查是否有有效的捕获数据
        if (
          !sessionData.capturedSize ||
          (sessionData.capturedSize.video === 0 &&
            sessionData.capturedSize.audio === 0)
        ) {
          return false;
        }

        return true;
      }

      // v3.7.1 新增：检查会话有效性
      isSessionValid(sessionData) {
        const now = Date.now();
        const sessionAge = now - sessionData.timestamp;

        // 会话不能超过24小时
        if (sessionAge > 24 * 60 * 60 * 1000) {
          this.logger.debug(
            `Session expired: ${sessionAge / 1000 / 60 / 60} hours old`,
          );
          return false;
        }

        // 检查必要的字段
        if (!sessionData.url || !sessionData.timestamp) {
          this.logger.debug("Session missing required fields");
          return false;
        }

        return true;
      }

      // v3.8.0 重构：增强的视频ID提取器（支持多网站）
      extractVideoId(url) {
        try {
          const hostname = new URL(url).hostname.toLowerCase();

          // 基于域名的专属策略调度器
          switch (true) {
            case hostname.includes("youtube.com") ||
              hostname.includes("youtu.be"):
              return this.extractYouTubeId(url);

            case hostname.includes("bilibili.com"):
              return this.extractBilibiliId(url);

            case hostname.includes("vimeo.com"):
              return this.extractVimeoId(url);

            case hostname.includes("twitch.tv"):
              return this.extractTwitchId(url);

            case hostname.includes("netflix.com"):
              return this.extractNetflixId(url);

            default:
              // 回退到通用匹配策略
              return this.extractGenericVideoId(url);
          }
        } catch (e) {
          this.logger.error("Error extracting video ID:", e);
          return null;
        }
      }

      // v3.8.0 新增：YouTube专属ID提取
      extractYouTubeId(url) {
        // 标准格式: youtube.com/watch?v=ID
        let match = url.match(/[?&]v=([^&]+)/);
        if (match) return match[1];

        // 短链格式: youtu.be/ID
        match = url.match(/youtu\.be\/([^?]+)/);
        if (match) return match[1];

        // 嵌入格式: youtube.com/embed/ID
        match = url.match(/\/embed\/([^?]+)/);
        if (match) return match[1];

        return null;
      }

      // v3.8.0 新增：Bilibili专属ID提取
      extractBilibiliId(url) {
        // BV号格式: bilibili.com/video/BV...
        let match = url.match(/\/video\/(BV[^/?]+)/);
        if (match) return match[1];

        // AV号格式: bilibili.com/video/av...
        match = url.match(/\/video\/(av\d+)/);
        if (match) return match[1];

        return null;
      }

      // v3.8.0 新增：Vimeo专属ID提取
      extractVimeoId(url) {
        // 标准格式: vimeo.com/ID
        const match = url.match(/vimeo\.com\/(\d+)/);
        return match ? match[1] : null;
      }

      // v3.8.0 新增：Twitch专属ID提取
      extractTwitchId(url) {
        // 视频格式: twitch.tv/videos/ID
        let match = url.match(/\/videos\/(\d+)/);
        if (match) return match[1];

        // 直播格式: twitch.tv/channel
        match = url.match(/twitch\.tv\/([^/?]+)/);
        if (match && !["videos", "directory", "p"].includes(match[1])) {
          return match[1]; // 频道名作为ID
        }

        return null;
      }

      // v3.8.0 新增：Netflix专属ID提取
      extractNetflixId(url) {
        // Netflix格式: netflix.com/watch/ID
        const match = url.match(/\/watch\/(\d+)/);
        return match ? match[1] : null;
      }

      // v3.9.0 修复：收紧通用视频ID提取（回退策略）
      extractGenericVideoId(url) {
        // v3.9.0 修复：更严格的视频ID模式，避免误判
        const patterns = [
          /[?&]v=([^&]+)/, // ?v=ID
          /[?&]id=([^&]+)/, // ?id=ID
          /[?&]video_id=([^&]+)/, // ?video_id=ID
          /\/video\/([^/?]+)/, // /video/ID
          /\/watch\/([^/?]+)/, // /watch/ID
          /\/v\/([^/?]+)/, // /v/ID
          /\/embed\/([^/?]+)/, // /embed/ID
          // v3.9.0 修复：移除过于宽泛的长字符串匹配，改为更严格的规则
          /\/([a-zA-Z0-9_-]{10,}[0-9]+[a-zA-Z0-9_-]*)/, // 至少10位且包含数字的ID
          /\/([0-9]{6,})/, // 纯数字ID（至少6位）
        ];

        for (const pattern of patterns) {
          const match = url.match(pattern);
          if (match && match[1]) {
            const id = match[1];
            // v3.9.0 增强：额外验证，排除明显的非ID内容
            if (this.isValidVideoId(id)) {
              return id;
            }
          }
        }

        return null;
      }

      // v3.9.0 新增：验证视频ID的有效性
      isValidVideoId(id) {
        // 排除明显的非ID内容
        const invalidPatterns = [
          /^(home|about|contact|help|support|login|register|search|category|categories)$/i,
          /^(page|pages|post|posts|article|articles|news|blog|blogs)$/i,
          /^(user|users|profile|profiles|account|accounts)$/i,
          /^(admin|administrator|moderator|staff)$/i,
          /^(api|ajax|json|xml|rss|feed)$/i,
          /^(css|js|javascript|style|styles|script|scripts)$/i,
          /^(img|image|images|pic|pics|photo|photos)$/i,
        ];

        // 检查是否匹配无效模式
        for (const pattern of invalidPatterns) {
          if (pattern.test(id)) {
            return false;
          }
        }

        // 基本长度和字符检查
        if (id.length < 3 || id.length > 50) {
          return false;
        }

        // 必须包含字母或数字
        if (!/[a-zA-Z0-9]/.test(id)) {
          return false;
        }

        return true;
      }

      // v3.6.0 新增：确定恢复级别
      determineResumeLevel(sessionData) {
        const now = Date.now();
        const timeDiff = now - sessionData.timestamp;
        const currentUrl = window.location.href;

        // 同页面且时间较近（5分钟内）
        if (sessionData.url === currentUrl && timeDiff < 300000) {
          return "FULL";
        }

        // 同视频但不同页面或时间较久
        return "PARTIAL";
      }

      // v3.7.1 修复：增强的数据完整性检查
      async checkDataIntegrity(sessionId) {
        try {
          // 检查会话数据
          const sessionData =
            this.heartbeatManager.getAllStoredSessions()[sessionId];
          if (!sessionData || !sessionData.capturedSize) {
            return false;
          }

          // 检查是否有捕获数据
          const hasData =
            sessionData.capturedSize.video > 0 ||
            sessionData.capturedSize.audio > 0;
          if (!hasData) {
            return false;
          }

          // v3.7.1 新增：检查实际存储的数据
          try {
            const videoChunks = await this.dbManager.getChunks(
              sessionId,
              "video",
            );
            const audioChunks = await this.dbManager.getChunks(
              sessionId,
              "audio",
            );

            // 至少要有一种类型的数据块
            const hasActualData =
              videoChunks.length > 0 || audioChunks.length > 0;

            if (!hasActualData) {
              this.logger.warn(
                `Session ${sessionId} has size data but no actual chunks`,
              );
              return false;
            }

            return true;
          } catch (dbError) {
            this.logger.error(`Database error checking ${sessionId}:`, dbError);
            return false;
          }
        } catch (e) {
          this.logger.error(
            `Error checking data integrity for ${sessionId}:`,
            e,
          );
          return false;
        }
      }

      // v3.6.0 新增：显示恢复选择对话框
      showResumeDialog(resumableSessions) {
        this.logger.info("Showing resume dialog for user selection");

        // 通过GUI管理器显示恢复对话框
        this.guiManager.showResumeDialog(resumableSessions);
      }

      // v3.7.0 增强：真正的断点续传逻辑
      async resumeSessionEnhanced(sessionId, resumeLevel) {
        const sessionData =
          this.heartbeatManager.getAllStoredSessions()[sessionId];
        if (!sessionData) {
          this.logger.error(`Session data not found for ${sessionId}`);
          throw new Error("会话数据不存在");
        }

        this.logger.info(
          `Resuming session ${sessionId} with level: ${resumeLevel}`,
        );

        try {
          // v3.7.0 核心：创建续传会话对象
          const resumedSession = await this.createResumedSession(
            sessionId,
            sessionData,
          );

          // 注册到当前会话管理
          this.sessions.set(sessionId, resumedSession);

          // 更新心跳
          this.heartbeatManager.updateSession(
            sessionId,
            resumedSession.getHeartbeatData(),
          );

          // 更新GUI
          this.guiManager.selectSession(sessionId);
          this.guiManager.updateSessionList();

          // v3.8.0 重构：使用统一的恢复策略执行器
          await this.executeResumeStrategy(
            sessionId,
            sessionData,
            resumedSession,
            resumeLevel,
          );
        } catch (error) {
          this.logger.error(`Failed to resume session ${sessionId}:`, error);
          throw error;
        }
      }

      // v3.7.1 修复：安全的续传会话创建
      async createResumedSession(sessionId, sessionData) {
        this.logger.info(`Creating resumed session for ${sessionId}`);

        // v3.7.1 修复：检查会话ID冲突
        if (this.sessions.has(sessionId)) {
          throw new Error(`Session ID conflict: ${sessionId} already exists`);
        }

        // 创建新的会话对象，但保留历史数据
        const resumedSession = new CaptureSession(sessionId, null, true); // 第三个参数表示是续传会话

        // 恢复会话状态
        resumedSession.state = "resuming";
        resumedSession.capturedSize = sessionData.capturedSize || {
          video: 0,
          audio: 0,
        };
        resumedSession.startTime = sessionData.timestamp || Date.now();
        resumedSession.resumeTime = Date.now();
        resumedSession.isResumed = true;

        // v3.7.1 修复：安全的数据加载
        try {
          const loadResult = await this.loadSessionDataSafely(sessionId);

          if (loadResult.success) {
            // 预准备Blob URLs（限制大小）
            if (loadResult.videoChunks.length > 0) {
              const totalVideoSize = loadResult.videoChunks.reduce(
                (sum, c) => sum + c.data.byteLength,
                0,
              );

              // v3.9.0 修复：大文件处理策略
              if (totalVideoSize > 500 * 1024 * 1024) {
                this.logger.warn(
                  `Video data too large (${totalVideoSize / 1024 / 1024}MB), enabling segmented export`,
                );
                resumedSession.largeDataWarning = true;
                resumedSession.largeVideoData = loadResult.videoChunks; // 保存原始数据块用于分段导出
              } else {
                const videoBlob = new Blob(
                  loadResult.videoChunks.map((c) => c.data),
                  { type: "video/webm" },
                );
                resumedSession.preparedBlobs.video =
                  URL.createObjectURL(videoBlob);
              }
            }

            if (loadResult.audioChunks.length > 0) {
              const totalAudioSize = loadResult.audioChunks.reduce(
                (sum, c) => sum + c.data.byteLength,
                0,
              );

              if (totalAudioSize > 100 * 1024 * 1024) {
                // 音频限制100MB
                this.logger.warn(
                  `Audio data too large (${totalAudioSize / 1024 / 1024}MB), enabling segmented export`,
                );
                resumedSession.largeDataWarning = true;
                resumedSession.largeAudioData = loadResult.audioChunks; // 保存原始数据块用于分段导出
              } else {
                const audioBlob = new Blob(
                  loadResult.audioChunks.map((c) => c.data),
                  { type: "audio/webm" },
                );
                resumedSession.preparedBlobs.audio =
                  URL.createObjectURL(audioBlob);
              }
            }

            this.logger.info(`Session ${sessionId} data loaded successfully`);
          } else {
            this.logger.warn(
              `Failed to load data for ${sessionId}: ${loadResult.error}`,
            );
            resumedSession.dataIncomplete = true;
          }
        } catch (error) {
          this.logger.error(
            `Failed to load stored data for ${sessionId}:`,
            error,
          );
          resumedSession.dataIncomplete = true;
          // 不抛出错误，允许续传会话创建，但标记为数据不完整
        }

        return resumedSession;
      }

      // v3.7.1 新增：安全的数据加载
      async loadSessionDataSafely(sessionId) {
        try {
          const videoChunks = await this.dbManager.getChunks(
            sessionId,
            "video",
          );
          const audioChunks = await this.dbManager.getChunks(
            sessionId,
            "audio",
          );

          this.logger.info(
            `Loaded ${videoChunks.length} video chunks, ${audioChunks.length} audio chunks for ${sessionId}`,
          );

          // v3.7.1 新增：验证数据连续性
          const videoGaps = this.findDataGaps(videoChunks);
          const audioGaps = this.findDataGaps(audioChunks);

          if (videoGaps.length > 0) {
            this.logger.warn(`Found ${videoGaps.length} gaps in video data`);
          }

          if (audioGaps.length > 0) {
            this.logger.warn(`Found ${audioGaps.length} gaps in audio data`);
          }

          return {
            success: true,
            videoChunks,
            audioChunks,
            videoGaps,
            audioGaps,
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            videoChunks: [],
            audioChunks: [],
          };
        }
      }

      // v3.7.1 新增：查找数据间隙
      findDataGaps(chunks) {
        if (chunks.length === 0) return [];

        const gaps = [];
        chunks.sort((a, b) => a.index - b.index);

        for (let i = 1; i < chunks.length; i++) {
          const expectedIndex = chunks[i - 1].index + 1;
          if (chunks[i].index !== expectedIndex) {
            gaps.push({
              start: expectedIndex,
              end: chunks[i].index - 1,
            });
          }
        }

        return gaps;
      }

      // v3.8.0 新增：检查会话数据间隙
      async checkSessionDataGaps(sessionId) {
        try {
          const videoChunks = await this.dbManager.getChunks(
            sessionId,
            "video",
          );
          const audioChunks = await this.dbManager.getChunks(
            sessionId,
            "audio",
          );

          const videoGaps = this.findDataGaps(videoChunks);
          const audioGaps = this.findDataGaps(audioChunks);

          return {
            videoGaps,
            audioGaps,
            hasGaps: videoGaps.length > 0 || audioGaps.length > 0,
          };
        } catch (error) {
          this.logger.error(
            `Failed to check data gaps for ${sessionId}:`,
            error,
          );
          return {
            videoGaps: [],
            audioGaps: [],
            hasGaps: false,
          };
        }
      }

      // v3.8.0 新增：强制重新扫描所有会话
      async forceRescanSessions() {
        this.logger.info("Force rescanning all sessions...");

        try {
          const allStoredSessions =
            this.heartbeatManager.getAllStoredSessions();
          const validSessions = [];

          for (const [sessionId, sessionData] of Object.entries(
            allStoredSessions,
          )) {
            this.logger.debug(`Checking session ${sessionId}`);

            // 放宽检查条件，只要有数据就认为可续传
            if (
              sessionData.capturedSize &&
              (sessionData.capturedSize.video > 0 ||
                sessionData.capturedSize.audio > 0)
            ) {
              // 检查实际数据存在性
              const hasData = await this.checkDataIntegrity(sessionId);

              if (hasData) {
                // v3.8.0 增强：检查数据间隙
                const dataGaps = await this.checkSessionDataGaps(sessionId);

                const resumeInfo = {
                  sessionId,
                  sessionData,
                  resumeLevel: "PARTIAL", // 强制扫描默认为部分恢复
                  dataIntegrity: hasData,
                  estimatedSize: sessionData.capturedSize,
                  lastActiveTime: sessionData.timestamp,
                  videoId: this.extractVideoId(sessionData.url),
                  dataGaps: dataGaps, // v3.8.0 新增：数据间隙信息
                };
                validSessions.push(resumeInfo);
              }
            }
          }

          this.logger.info(
            `Force scan found ${validSessions.length} valid sessions`,
          );
          return validSessions.sort(
            (a, b) => b.lastActiveTime - a.lastActiveTime,
          );
        } catch (error) {
          this.logger.error("Force rescan failed:", error);
          throw new Error("扫描失败: " + error.message);
        }
      }

      // v3.8.0 新增：强制锁定当前页面
      async forceLockCurrentPage() {
        this.logger.info("Force locking current page for resume...");

        try {
          const currentVideoId = this.extractVideoId(window.location.href);
          if (!currentVideoId) {
            throw new Error("无法识别当前视频ID");
          }

          // 创建强制锁定标记
          const lockData = {
            videoId: currentVideoId,
            url: window.location.href,
            timestamp: Date.now(),
            locked: true,
            lockType: "force",
          };

          // 存储锁定信息
          localStorage.setItem("aria_force_lock", JSON.stringify(lockData));

          // 设置强制续传标志
          this.forceLocked = true;
          this.forceLockedVideoId = currentVideoId;

          this.logger.info(`Force locked video ID: ${currentVideoId}`);
        } catch (error) {
          this.logger.error("Force lock failed:", error);
          throw new Error("锁定失败: " + error.message);
        }
      }

      // v3.8.0 新增：检查强制锁定状态
      checkForceLock() {
        try {
          const lockData = localStorage.getItem("aria_force_lock");
          if (!lockData) return false;

          const lock = JSON.parse(lockData);
          const currentVideoId = this.extractVideoId(window.location.href);

          // 检查是否匹配当前视频
          if (lock.videoId === currentVideoId && lock.locked) {
            this.logger.info(
              `Force lock detected for video: ${currentVideoId}`,
            );

            // 清除锁定标记
            localStorage.removeItem("aria_force_lock");

            // 设置强制续传模式
            this.forceLocked = true;
            this.forceLockedVideoId = currentVideoId;

            return true;
          }
        } catch (error) {
          this.logger.error("Check force lock failed:", error);
        }

        return false;
      }

      // v3.9.0 新增：处理初始化期间的待处理会话
      processPendingSessions() {
        if (this.pendingSessions.length === 0) {
          this.logger.info("No pending sessions to process");
          return;
        }

        this.logger.info(
          `Processing ${this.pendingSessions.length} pending sessions`,
        );

        // 逐个处理待处理的会话
        const sessionsToProcess = [...this.pendingSessions];
        this.pendingSessions = []; // 清空队列

        sessionsToProcess.forEach((session, index) => {
          // 延迟处理，避免同时处理太多会话
          setTimeout(() => {
            this.logger.info(
              `Processing pending session ${index + 1}/${sessionsToProcess.length}: ${session.id}`,
            );
            this.registerSession(session);
          }, index * 100); // 每个会话间隔100ms
        });
      }

      // v3.8.0 重构：统一的恢复处理（替代分散的恢复方法）
      async executeResumeStrategy(
        sessionId,
        sessionData,
        resumedSession,
        resumeLevel,
      ) {
        this.logger.info(
          `Executing ${resumeLevel} resume strategy for session ${sessionId}`,
        );

        try {
          switch (resumeLevel) {
            case "FULL":
              // 完整恢复：关联视频元素并恢复播放位置
              await this.associateVideoElement(resumedSession);
              const currentTime = sessionData.currentTime || 0;
              if (currentTime > 0) {
                this.handleUrlResume(sessionId, currentTime.toString());
              }
              break;

            case "PARTIAL":
              // 部分恢复：等待新的MediaSource关联
              this.logger.info(
                `Partial resume: waiting for new MediaSource association`,
              );
              break;

            case "MANUAL":
              // 手动恢复：显示用户指导
              this.guiManager.showNotification(
                "手动恢复模式：请手动播放视频继续捕获",
                "info",
              );
              break;
          }

          // 更新GUI状态
          this.guiManager.selectSession(sessionId);
          this.guiManager.updateSessionList();
          this.guiManager.updateStatus();
        } catch (error) {
          this.logger.error(`Resume strategy execution failed:`, error);
          throw error;
        }
      }

      // v3.8.0 新增：视频元素关联辅助方法
      async associateVideoElement(session) {
        const videoElement = this._findVideoElement();
        if (videoElement) {
          session.videoElement = videoElement;
          this.attachSpeedController(session);
          this.logger.info(
            `Video element associated for resumed session ${session.id}`,
          );
        } else {
          this.logger.warn(
            `No video element found for resumed session ${session.id}`,
          );
        }
      }

      // v3.6.1 新增：获取存储信息
      async getStorageInfo() {
        try {
          const allSessions = this.heartbeatManager.getAllStoredSessions();
          let totalSize = 0;
          let sessionCount = 0;

          // 计算所有会话的数据大小
          for (const [sessionId, sessionData] of Object.entries(allSessions)) {
            sessionCount++;
            if (sessionData.capturedSize) {
              totalSize +=
                (sessionData.capturedSize.video || 0) +
                (sessionData.capturedSize.audio || 0);
            }

            // 尝试从IndexedDB获取实际存储大小
            try {
              const chunks = await this.dbManager.getChunks(sessionId, "video");
              chunks.forEach((chunk) => (totalSize += chunk.data.byteLength));

              const audioChunks = await this.dbManager.getChunks(
                sessionId,
                "audio",
              );
              audioChunks.forEach(
                (chunk) => (totalSize += chunk.data.byteLength),
              );
            } catch (e) {
              // 忽略单个会话的错误
            }
          }

          return {
            totalSize,
            sessionCount,
            currentSessionId: this.getCurrentSessionId(),
          };
        } catch (error) {
          this.logger.error("Failed to get storage info:", error);
          return {
            totalSize: 0,
            sessionCount: 0,
            currentSessionId: null,
          };
        }
      }

      // v3.6.1 新增：清理数据库（保留当前会话）
      async cleanDatabaseExceptCurrent() {
        const currentSessionId = this.getCurrentSessionId();
        this.logger.info(
          `Starting database cleanup, preserving current session: ${currentSessionId}`,
        );

        try {
          // 获取所有存储的会话
          const allSessions = this.heartbeatManager.getAllStoredSessions();
          const sessionsToDelete = [];

          // 确定要删除的会话
          for (const sessionId of Object.keys(allSessions)) {
            if (sessionId !== currentSessionId) {
              sessionsToDelete.push(sessionId);
            }
          }

          this.logger.info(
            `Found ${sessionsToDelete.length} sessions to delete`,
          );

          // 删除IndexedDB中的数据
          for (const sessionId of sessionsToDelete) {
            try {
              await this.dbManager.deleteSession(sessionId);
              this.logger.debug(`Deleted session data: ${sessionId}`);
            } catch (error) {
              this.logger.error(
                `Failed to delete session ${sessionId}:`,
                error,
              );
            }
          }

          // 清理localStorage中的心跳数据
          const cleanedSessions = {};
          if (currentSessionId && allSessions[currentSessionId]) {
            cleanedSessions[currentSessionId] = allSessions[currentSessionId];
          }

          localStorage.setItem(
            "aria_active_sessions",
            JSON.stringify(cleanedSessions),
          );

          // 清理内存中的会话（保留当前会话）
          const currentSession = this.sessions.get(currentSessionId);
          this.sessions.clear();
          if (currentSession) {
            this.sessions.set(currentSessionId, currentSession);
          }

          // 更新GUI
          this.guiManager.updateSessionList();

          this.logger.info(
            `Database cleanup completed. Deleted ${sessionsToDelete.length} sessions.`,
          );

          return {
            deletedSessions: sessionsToDelete.length,
            preservedSession: currentSessionId,
          };
        } catch (error) {
          this.logger.error("Database cleanup failed:", error);
          throw new Error("清理失败: " + error.message);
        }
      }

      // v3.6.1 新增：获取当前会话ID
      getCurrentSessionId() {
        // 优先返回GUI中选中的会话
        if (this.guiManager.currentSessionId) {
          return this.guiManager.currentSessionId;
        }

        // 如果没有选中的会话，返回最新的活跃会话
        const activeSessions = Array.from(this.sessions.keys());
        if (activeSessions.length > 0) {
          return activeSessions[activeSessions.length - 1];
        }

        return null;
      }

      // v3.6.2 紧急修复：检查存储使用情况
      async checkStorageUsage() {
        try {
          if ("storage" in navigator && "estimate" in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            const usageRatio = estimate.usage / estimate.quota;

            this.logger.info(
              `Storage usage: ${(usageRatio * 100).toFixed(1)}% (${(estimate.usage / 1024 / 1024).toFixed(1)}MB / ${(estimate.quota / 1024 / 1024).toFixed(1)}MB)`,
            );

            // 关键阈值检查
            if (usageRatio >= ARIA_USER_CONFIG.STORAGE_CRITICAL_THRESHOLD) {
              this.logger.warn(
                "🚨 Critical storage usage detected! Forcing cleanup...",
              );
              await this.handleCriticalStorage();
            } else if (
              usageRatio >= ARIA_USER_CONFIG.STORAGE_WARNING_THRESHOLD
            ) {
              this.logger.warn("⚠️ High storage usage detected!");
              this.showStorageWarning(usageRatio);
            }
          }
        } catch (error) {
          this.logger.error("Failed to check storage usage:", error);
        }
      }

      // v3.6.2 紧急修复：处理关键存储情况
      async handleCriticalStorage() {
        const logger = this.logger; // v3.8.0 修复：确保logger引用正确
        try {
          // 强制清理所有历史数据
          logger.info("Performing emergency cleanup...");

          // 清理localStorage
          const keys = Object.keys(localStorage);
          keys.forEach((key) => {
            if (key.startsWith("aria_") && key !== "aria_settings") {
              localStorage.removeItem(key);
            }
          });

          // v3.9.0 修复：清理IndexedDB with onblocked handler
          try {
            const databases = await indexedDB.databases();
            for (const db of databases) {
              if (db.name && db.name.includes("Aria")) {
                const deleteRequest = indexedDB.deleteDatabase(db.name);

                // v3.9.0 关键修复：添加阻塞事件处理器
                deleteRequest.onblocked = () => {
                  logger.error(
                    `Emergency cleanup blocked for database: ${db.name}`,
                  );
                  this.guiManager.showNotification(
                    "紧急清理被其他页面阻止！请关闭所有相关标签页后刷新页面。",
                    "error",
                  );
                };

                deleteRequest.onsuccess = () => {
                  logger.info(`Successfully deleted database: ${db.name}`);
                };

                deleteRequest.onerror = (event) => {
                  logger.error(
                    `Failed to delete database ${db.name}:`,
                    event.target.error,
                  );
                };
              }
            }
          } catch (e) {
            logger.error("Failed to cleanup IndexedDB:", e);
          }

          // 显示紧急清理通知
          this.guiManager.showEmergencyCleanupNotice();
        } catch (error) {
          logger.error("Emergency cleanup failed:", error);
          this.guiManager.showNotification(
            "紧急清理失败，请手动清理浏览器数据",
            "error",
          );
        }
      }

      // v3.6.2 紧急修复：显示存储警告
      showStorageWarning(usageRatio) {
        const percentage = (usageRatio * 100).toFixed(1);
        this.guiManager.showNotification(
          `存储空间不足！已使用 ${percentage}%，建议立即清理数据库`,
          "warning",
        );

        // 自动显示清理对话框
        setTimeout(() => {
          this.guiManager.showCleanDatabaseDialog();
        }, 2000);
      }

      // v3.9.0 智能会话注册：支持初始化状态锁和续传会话阻止
      registerSession(session) {
        // v3.9.0 关键修复：检查初始化状态
        if (this.isInitializing) {
          this.logger.warn(
            `Blocking new session registration during initialization: ${session.id}`,
          );
          // 将会话加入待处理队列
          this.pendingSessions.push(session);
          this.logger.info(
            `Session ${session.id} queued for post-initialization processing`,
          );
          return;
        }

        // v3.8.0 新增：检查是否应该阻止新会话
        if (this.blockNewSessions) {
          this.logger.info(
            `Blocking new session ${session.id} due to active resume operation`,
          );
          return;
        }

        this.logger.info(`Registering session ${session.id}`);
        this.sessions.set(session.id, session);

        // 添加到心跳管理器
        this.heartbeatManager.updateSession(
          session.id,
          session.getHeartbeatData(),
        );

        this.guiManager.selectSession(session.id);
        this.guiManager.updateSessionList();

        // v3.8.0 改进：更稳定的视频元素关联
        const observer = new MutationObserver((mutations, obs) => {
          // 每次DOM变化都尝试查找视频元素
          const videoElement = this._findVideoElement();
          if (videoElement) {
            // 找到视频元素后立即断开观察器
            obs.disconnect();
            this.logger.info(
              `MutationObserver successfully found video element for session ${session.id}`,
            );

            // 礼貌激活延迟
            setTimeout(() => {
              this._activateSessionForVideo(session, videoElement);
            }, ARIA_USER_CONFIG.ACTIVATION_DELAY);
          }
        });

        // 立即尝试查找视频元素
        const videoElement = this._findVideoElement();
        if (videoElement) {
          setTimeout(() => {
            this._activateSessionForVideo(session, videoElement);
          }, ARIA_USER_CONFIG.ACTIVATION_DELAY);
        } else {
          // 如果没有找到，启动 MutationObserver
          this.logger.info(
            `Starting MutationObserver for session ${session.id}`,
          );

          observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributeFilter: ["src"],
          });

          // 安全超时
          setTimeout(() => {
            observer.disconnect();
            this.logger.warn(
              `Video element association timed out for session ${session.id}`,
            );
          }, ARIA_USER_CONFIG.VIDEO_ELEMENT_TIMEOUT);
        }
      }

      // v3.5.0 智能查找：纯查找功能，职责单一
      _findVideoElement() {
        try {
          const allVideos = Array.from(document.querySelectorAll("video"));
          this.logger.debug(`Found ${allVideos.length} total video elements`);

          const blobVideos = allVideos.filter((v) => {
            const hasSrc = v.src && typeof v.src === "string";
            const isBlob = hasSrc && v.src.startsWith("blob:");
            this.logger.debug(
              `Video element: src="${v.src || "none"}", readyState=${v.readyState}, isBlob=${isBlob}`,
            );
            return isBlob;
          });

          if (blobVideos.length > 0) {
            // 优先选择正在播放的视频
            const playingVideo = blobVideos.find(
              (v) => !v.paused && v.readyState > 2,
            );
            const selectedVideo =
              playingVideo ||
              blobVideos.find((v) => v.readyState > 0) ||
              blobVideos[0];

            this.logger.debug(
              `Selected video: readyState=${selectedVideo.readyState}, paused=${selectedVideo.paused}`,
            );
            return selectedVideo;
          }

          return null;
        } catch (e) {
          this.logger.error("Error in _findVideoElement:", e);
          return null;
        }
      }

      // v3.5.0 智能激活：专门负责激活会话与视频元素的交互
      _activateSessionForVideo(session, videoElement) {
        try {
          session.videoElement = videoElement;

          this.logger.info(
            `Activated session ${session.id} with video element (智能礼貌激活)`,
          );
          this.logger.debug(
            `Video details: readyState=${videoElement.readyState}, currentTime=${videoElement.currentTime}, duration=${videoElement.duration}`,
          );

          // 附加速度控制器
          this.attachSpeedController(session);

          // 更新UI状态
          this.guiManager.updateStatus();
        } catch (e) {
          this.logger.error(
            `Error in _activateSessionForVideo for ${session.id}:`,
            e,
          );
        }
      }

      // v3.4.3 新增：监控传统视频播放（非MediaSource）
      monitorTraditionalVideo() {
        this.logger.info("Setting up traditional video monitoring...");

        // 监控所有视频元素的 src 变化
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (
              mutation.type === "attributes" &&
              mutation.attributeName === "src"
            ) {
              const video = mutation.target;
              if (video.tagName === "VIDEO" && video.src) {
                this.logger.info(
                  `Video src changed: ${video.src.substring(0, 100)}...`,
                );

                // 检查是否是YouTube的直接视频URL
                if (
                  video.src.includes("googlevideo.com") ||
                  video.src.includes("youtube.com")
                ) {
                  this.logger.warn(
                    "Detected traditional YouTube video playback - MediaSource bypass detected!",
                  );
                  this.logger.info(`Full URL: ${video.src}`);
                }
              }
            }

            // 监控新增的视频元素
            if (mutation.type === "childList") {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  const videos =
                    node.tagName === "VIDEO"
                      ? [node]
                      : node.querySelectorAll
                        ? Array.from(node.querySelectorAll("video"))
                        : [];
                  videos.forEach((video) => {
                    this.logger.info(
                      `New video element detected: ${video.src || "no src yet"}`,
                    );

                    // 监听 loadstart 事件
                    video.addEventListener("loadstart", () => {
                      this.logger.info(
                        `Video loadstart: ${video.src.substring(0, 100)}...`,
                      );
                    });

                    // 监听 canplay 事件
                    video.addEventListener("canplay", () => {
                      this.logger.info(
                        `Video canplay: ${video.src.substring(0, 100)}...`,
                      );
                    });
                  });
                }
              });
            }
          });
        });

        observer.observe(document.body || document.documentElement, {
          childList: true,
          subtree: true,
          attributes: true,
          attributeFilter: ["src"],
        });

        // 检查已存在的视频元素
        setTimeout(() => {
          const existingVideos = Array.from(document.querySelectorAll("video"));
          this.logger.info(
            `Found ${existingVideos.length} existing video elements`,
          );
          existingVideos.forEach((video, index) => {
            this.logger.info(
              `Video ${index + 1}: src="${video.src || "none"}", readyState=${video.readyState}`,
            );
          });
        }, 1000);
      }

      // v3.2.0 核心重构：纯事件驱动的速度控制器附加
      attachSpeedController(session) {
        if (!session.videoElement) return;

        const onPlay = () => {
          // 只在明确的播放事件时启动速度控制器
          if (
            this.settings.enableSpeedControl &&
            session.state === "capturing"
          ) {
            if (!session.speedController) {
              session.speedController = new AdaptiveSpeedController(session);
            }
            session.speedController.start();
            this.logger.info(
              `Speed controller started on play event for session ${session.id}`,
            );
          }
        };

        const onPause = () => {
          if (session.speedController) {
            session.speedController.stop();
            this.logger.info(
              `Speed controller stopped on pause event for session ${session.id}`,
            );
          }
        };

        // 使用session的事件管理器
        session.addEventListener(session.videoElement, "play", onPlay);
        session.addEventListener(session.videoElement, "pause", onPause);

        // 检查当前状态，处理页面加载时视频就自动播放的情况
        if (
          !session.videoElement.paused &&
          session.videoElement.readyState >= 2
        ) {
          // 视频已经在播放，立即调用 play 回调
          onPlay();
        }
      }

      onSessionComplete(session) {
        // 更新心跳状态
        this.heartbeatManager.updateSession(
          session.id,
          session.getHeartbeatData(),
        );

        this.guiManager.showCompletionNotice();

        if (session.videoElement && !session.videoElement.paused) {
          try {
            session.videoElement.pause();
            this.logger.info(`Session ${session.id} complete. Video paused.`);
          } catch (e) {
            this.logger.error("Failed to pause video:", e);
          }
        }

        if (this.settings.autoDownload) {
          this.downloadSession(session.id, "both");
        }
      }

      // 断点续传逻辑
      resumeSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) return;

        const currentTime = session.videoElement?.currentTime || 0;
        const url = new URL(window.location.href);

        url.searchParams.set("aria_resume_id", sessionId);
        url.searchParams.set("aria_resume_time", currentTime.toString());

        window.location.href = url.toString();
      }

      // 完全同步的下载方法（最终版）
      downloadSession(sessionId, type) {
        const session = this.sessions.get(sessionId);
        if (!session) {
          this.logger.error(`Session ${sessionId} not found`);
          return;
        }

        // v3.9.0 增强：大文件分段导出策略
        if (session.largeDataWarning) {
          this.downloadLargeFileSegments(session, type);
          return;
        }

        // 只使用预准备的Blob URLs（完全同步）
        const download = (blobUrl, filename, fileType) => {
          if (!blobUrl) {
            this.logger.warn(`No blob URL available for ${filename}`);
            return;
          }

          const a = document.createElement("a");
          a.href = blobUrl;
          a.download = filename;
          a.style.display = "none";

          document.body.appendChild(a);

          try {
            a.click();
            this.logger.info(`Download triggered for ${filename}`);

            // 计划释放Blob URL
            session.scheduleBlobUrlRevoke(fileType, blobUrl);
          } catch (e) {
            this.logger.error("Download failed:", e);
          }

          // 快速移除DOM元素
          setTimeout(() => {
            document.body.removeChild(a);
          }, 100);
        };

        // 生成安全的文件名
        const safeFilename = (document.title || "video")
          .replace(/[\\/:*?"<>|]/g, "-")
          .substring(0, 100);

        // 立即连续触发下载
        if (type === "video" || type === "both") {
          const videoUrl = session.preparedBlobs.video;
          if (videoUrl) {
            const ext = session.fileExtensions.video;
            download(videoUrl, `${safeFilename}_video${ext}`, "video");
          }
        }

        if (type === "audio" || type === "both") {
          const audioUrl = session.preparedBlobs.audio;
          if (audioUrl) {
            const ext = session.fileExtensions.audio;
            download(audioUrl, `${safeFilename}_audio${ext}`, "audio");
          }
        }
      }

      // v3.9.0 新增：大文件分段导出功能
      downloadLargeFileSegments(session, type) {
        const SEGMENT_SIZE = 256 * 1024 * 1024; // 256MB per segment

        const safeFilename = (document.title || "video")
          .replace(/[\\/:*?"<>|]/g, "-")
          .substring(0, 100);

        const downloadSegment = (blob, filename) => {
          const a = document.createElement("a");
          a.href = URL.createObjectURL(blob);
          a.download = filename;
          a.style.display = "none";
          document.body.appendChild(a);
          a.click();

          setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(a.href);
          }, 100);
        };

        const createMergeInstructions = (fileType, segmentCount) => {
          const ext = session.fileExtensions[fileType] || ".webm";
          const instructions = `# ${fileType.toUpperCase()} 文件合并说明
# 请将所有分段文件放在同一目录下，然后运行以下命令：

# Windows (CMD):
copy /b "${safeFilename}_${fileType}_part*.bin" "${safeFilename}_${fileType}${ext}"

# Windows (PowerShell):
Get-Content "${safeFilename}_${fileType}_part*.bin" -Raw -Encoding Byte | Set-Content "${safeFilename}_${fileType}${ext}" -Encoding Byte

# Linux/Mac:
cat "${safeFilename}_${fileType}_part"*.bin > "${safeFilename}_${fileType}${ext}"

# 总共 ${segmentCount} 个分段文件
# 合并后请删除分段文件以节省空间
`;

          const instructionBlob = new Blob([instructions], {
            type: "text/plain",
          });
          downloadSegment(
            instructionBlob,
            `${safeFilename}_${fileType}_merge_instructions.txt`,
          );
        };

        // 处理视频分段
        if ((type === "video" || type === "both") && session.largeVideoData) {
          this.logger.info("Starting segmented video export...");

          let currentSize = 0;
          let segmentData = [];
          let segmentIndex = 1;

          for (let i = 0; i < session.largeVideoData.length; i++) {
            const chunk = session.largeVideoData[i];
            segmentData.push(chunk.data);
            currentSize += chunk.data.byteLength;

            if (
              currentSize >= SEGMENT_SIZE ||
              i === session.largeVideoData.length - 1
            ) {
              const segmentBlob = new Blob(segmentData, {
                type: "application/octet-stream",
              });
              const filename = `${safeFilename}_video_part${segmentIndex.toString().padStart(3, "0")}.bin`;

              setTimeout(
                () => {
                  downloadSegment(segmentBlob, filename);
                },
                (segmentIndex - 1) * 1000,
              ); // 1秒间隔

              segmentData = [];
              currentSize = 0;
              segmentIndex++;
            }
          }

          // 下载合并说明
          setTimeout(() => {
            createMergeInstructions("video", segmentIndex - 1);
          }, segmentIndex * 1000);
        }

        // 处理音频分段
        if ((type === "audio" || type === "both") && session.largeAudioData) {
          this.logger.info("Starting segmented audio export...");

          let currentSize = 0;
          let segmentData = [];
          let segmentIndex = 1;

          for (let i = 0; i < session.largeAudioData.length; i++) {
            const chunk = session.largeAudioData[i];
            segmentData.push(chunk.data);
            currentSize += chunk.data.byteLength;

            if (
              currentSize >= SEGMENT_SIZE ||
              i === session.largeAudioData.length - 1
            ) {
              const segmentBlob = new Blob(segmentData, {
                type: "application/octet-stream",
              });
              const filename = `${safeFilename}_audio_part${segmentIndex.toString().padStart(3, "0")}.bin`;

              setTimeout(
                () => {
                  downloadSegment(segmentBlob, filename);
                },
                (segmentIndex - 1) * 1000,
              ); // 1秒间隔

              segmentData = [];
              currentSize = 0;
              segmentIndex++;
            }
          }

          // 下载合并说明
          setTimeout(() => {
            createMergeInstructions("audio", segmentIndex - 1);
          }, segmentIndex * 1000);
        }

        this.guiManager.showNotification(
          "大文件分段导出已开始！请查看下载的说明文件了解如何合并分段。",
          "info",
        );
      }
    }

    AriaController.logger = new Logger();
    return AriaController;
  })();

  // 启动脚本
  (async function () {
    try {
      // 检查浏览器兼容性
      if (!window.MediaSource || typeof MediaSource !== "function") {
        console.warn("[Aria] MediaSource API not available");
        return;
      }

      if (!window.indexedDB) {
        console.error("[Aria] IndexedDB not available");
        return;
      }

      if (!window.Blob || !window.URL || !URL.createObjectURL) {
        console.error("[Aria] Required APIs not available");
        return;
      }

      // 等待基本DOM结构
      if (document.readyState === "loading") {
        await new Promise((resolve) => {
          const checkReady = () => {
            if (document.readyState !== "loading") {
              resolve();
            } else {
              setTimeout(checkReady, 10);
            }
          };
          checkReady();
        });
      }

      // 初始化
      await AriaDownloader.getInstance().init();

      console.log("[Aria] Ready to capture media");
    } catch (error) {
      console.error("[Aria] Fatal error during bootstrap:", error);
    }
  })();
})();
