<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aria Downloader v5.0.0 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-video {
            width: 100%;
            max-width: 640px;
            height: 360px;
            background: #000;
            border-radius: 4px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🎯 Aria Downloader v5.0.0 终极重构版测试</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>本页面用于测试Aria Downloader v5.0.0的核心功能：</p>
        <ul>
            <li>✅ MediaSource API劫持机制</li>
            <li>✅ 智能自适应变速控制</li>
            <li>✅ Shadow DOM隔离UI</li>
            <li>✅ IndexedDB数据持久化</li>
            <li>✅ 模块化站点提取器</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🎬 模拟视频播放器</h2>
        <video class="test-video" controls id="test-video">
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
            您的浏览器不支持视频播放。
        </video>
        
        <div style="margin-top: 15px;">
            <button class="test-button" onclick="testMediaSourceCapture()">🔥 测试MediaSource捕获</button>
            <button class="test-button" onclick="testSpeedControl()">🚀 测试智能变速</button>
            <button class="test-button" onclick="testUIFunctionality()">🛡️ 测试UI功能</button>
            <button class="test-button" onclick="clearTestData()">🧹 清理测试数据</button>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 测试日志</h2>
        <div class="log-output" id="test-log"></div>
    </div>

    <script>
        // 测试日志函数
        function logTest(message) {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[Test] ${message}`);
        }

        // 测试MediaSource捕获功能
        function testMediaSourceCapture() {
            logTest('开始测试MediaSource API劫持...');
            
            try {
                // 检查Aria是否已加载
                if (typeof window.AriaDebug !== 'undefined') {
                    logTest('✅ Aria调试接口已可用');
                    logTest(`✅ 活跃会话数: ${window.AriaDebug.sessions.size}`);
                    logTest(`✅ 配置加载: ${JSON.stringify(window.AriaDebug.config, null, 2)}`);
                } else {
                    logTest('⚠️ Aria调试接口不可用，可能需要启用调试模式');
                }

                // 检查MediaSource是否被劫持
                const originalToString = MediaSource.prototype.addSourceBuffer.toString();
                if (originalToString.includes('AriaController')) {
                    logTest('✅ MediaSource.addSourceBuffer已被成功劫持');
                } else {
                    logTest('❌ MediaSource.addSourceBuffer未被劫持');
                }

                // 模拟创建MediaSource
                const mediaSource = new MediaSource();
                logTest('✅ MediaSource实例创建成功');
                
                // 模拟添加SourceBuffer
                if (MediaSource.isTypeSupported('video/webm; codecs="vp8"')) {
                    const sourceBuffer = mediaSource.addSourceBuffer('video/webm; codecs="vp8"');
                    logTest('✅ SourceBuffer创建成功，应该触发会话注册');
                } else {
                    logTest('⚠️ 浏览器不支持WebM格式');
                }

            } catch (error) {
                logTest(`❌ MediaSource测试失败: ${error.message}`);
            }
        }

        // 测试智能变速功能
        function testSpeedControl() {
            logTest('开始测试智能变速控制...');
            
            const video = document.getElementById('test-video');
            if (!video) {
                logTest('❌ 找不到测试视频元素');
                return;
            }

            try {
                // 检查视频状态
                logTest(`✅ 视频当前状态: readyState=${video.readyState}, paused=${video.paused}`);
                
                // 测试播放速度调整
                video.playbackRate = 2.0;
                logTest(`✅ 播放速度设置为: ${video.playbackRate}x`);
                
                // 检查缓冲状态
                if (video.buffered.length > 0) {
                    const bufferedEnd = video.buffered.end(video.buffered.length - 1);
                    const bufferAhead = bufferedEnd - video.currentTime;
                    logTest(`✅ 缓冲状态: 当前时间=${video.currentTime.toFixed(2)}s, 缓冲结束=${bufferedEnd.toFixed(2)}s, 超前缓冲=${bufferAhead.toFixed(2)}s`);
                } else {
                    logTest('⚠️ 视频尚未开始缓冲');
                }

            } catch (error) {
                logTest(`❌ 变速测试失败: ${error.message}`);
            }
        }

        // 测试UI功能
        function testUIFunctionality() {
            logTest('开始测试UI功能...');
            
            try {
                // 检查Shadow DOM宿主
                const shadowHost = document.getElementById('aria-shadow-host');
                if (shadowHost) {
                    logTest('✅ Shadow DOM宿主元素已创建');
                    logTest(`✅ 宿主样式: ${shadowHost.style.cssText}`);
                } else {
                    logTest('❌ 找不到Shadow DOM宿主元素');
                }

                // 检查浮动按钮
                const toggleBtn = shadowHost?.shadowRoot?.getElementById('aria-toggle');
                if (toggleBtn) {
                    logTest('✅ 浮动切换按钮已创建');
                    // 模拟点击
                    toggleBtn.click();
                    logTest('✅ 模拟点击浮动按钮');
                } else {
                    logTest('❌ 找不到浮动切换按钮');
                }

            } catch (error) {
                logTest(`❌ UI测试失败: ${error.message}`);
            }
        }

        // 清理测试数据
        function clearTestData() {
            logTest('开始清理测试数据...');
            
            try {
                // 清理localStorage
                const keys = Object.keys(localStorage);
                const ariaKeys = keys.filter(key => key.startsWith('aria_'));
                ariaKeys.forEach(key => {
                    localStorage.removeItem(key);
                    logTest(`✅ 清理localStorage键: ${key}`);
                });

                // 清理GM存储
                try {
                    GM_deleteValue('aria_settings_v5');
                    GM_deleteValue('lastError');
                    logTest('✅ 清理GM存储完成');
                } catch (e) {
                    logTest(`⚠️ GM存储清理失败: ${e.message}`);
                }

                // 清理测试日志
                document.getElementById('test-log').textContent = '';
                logTest('✅ 测试数据清理完成');

            } catch (error) {
                logTest(`❌ 清理失败: ${error.message}`);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            logTest('🎯 Aria Downloader v5.0.0 终极重构版测试页面已加载');
            logTest('💡 请按顺序执行测试，观察右下角是否出现Aria浮动图标');
            
            // 等待Aria初始化
            setTimeout(() => {
                if (typeof window.AriaDebug !== 'undefined') {
                    logTest('🎉 Aria已成功初始化并启用调试模式');
                } else {
                    logTest('⚠️ Aria可能未完全初始化，或调试模式未启用');
                }
            }, 2000);
        });
    </script>
</body>
</html>
